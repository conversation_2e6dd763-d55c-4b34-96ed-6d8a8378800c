mutation UpdateTraining {
  updateTraining(
    input: {
      id: 1  # Replace with an actual training ID
      title: "Updated Training Title"
      description: "Updated training description"
      price: 199.99
      # Trying to update provider (this should be ignored)
      providerId: 999  # This should be ignored
      provider: "New Provider Name"  # This should be ignored
    }
  ) {
    id
    title
    description
    price
    # Check that provider info remains unchanged
    provider
    providerId
    providerEntity {
      id
      name
    }
  }
}
