NODE_ENV=production 
# PostgreSQL Database Configuration
PORT=3000
DB_HOST=shortline.proxy.rlwy.net
DB_TYPE=postgres
DB_PORT=28251
DB_USERNAME=postgres
DB_PASSWORD=bgOWxIMtmNWlIJGRcARRxVKkpOjdAtMW
DB_NAME=railway

# Optional Database Connection Parameters
DB_SCHEMA=public
DB_SYNCHRONIZE=true
DB_LOGGING=false

# GraphQL Configuration
GRAPHQL_PLAYGROUND=true
GRAPHQL_DEBUG=true

# Optional Environment Setting
NODE_ENV=development

LOG_LEVEL=debug

# Authentication
JWT_SECRET=your-super-secret-key
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_EXPIRES_IN=7d
BCRYPT_SALT_ROUNDS=10

# Full Database URL (alternative to individual settings)
DATABASE_URL=postgresql://postgres:<EMAIL>:28251/railway