import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not } from 'typeorm';
import { User } from '../shared/entities/user.entity';
import { UpdateProfileInput } from './dto/update-profile.input';
import { FileUpload } from 'graphql-upload';
import { FilesService } from 'src/files/files.service';
import * as path from 'path';
import * as fs from 'fs';
import { UserFilterInput } from './dto/user-filter.input';
import { PaginationMeta } from './responses/paginated-responce';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly filesService: FilesService, // Inject FilesService

  ) {}

  async findAll(): Promise<User[]> {
    return this.userRepository.find();
  }

  async updateProfile(userId: string, input: UpdateProfileInput): Promise<User> {
    console.log(userId);
    console.log(input);
    const user = await this.userRepository.findOne({
      where: { id: parseInt(userId) }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Create a clean input object with only defined values
    const cleanInput = {};

    // Copy all defined primitive fields
    Object.keys(input).forEach(key => {
      if (input[key] !== undefined &&
          key !== 'skills' &&
          key !== 'experiences' &&
          key !== 'certifications' &&
          key !== 'education') {
        cleanInput[key] = input[key];
      }
    });

    // Handle array fields to preserve existing values when undefined
    if (input.skills !== undefined) {
      cleanInput['skills'] = input.skills;
    }

    if (input.experiences !== undefined) {
      cleanInput['experiences'] = input.experiences;
    }

    if (input.certifications !== undefined) {
      cleanInput['certifications'] = input.certifications;
    }

    if (input.education !== undefined) {
      cleanInput['education'] = input.education;
    }

    // Apply changes to user
    Object.assign(user, cleanInput);
    return this.userRepository.save(user);
  }


  public async updateProfilepic(userId: string, profilePhoto?: FileUpload): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id: parseInt(userId) }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (profilePhoto) {
      const { createReadStream, filename, mimetype, encoding } = profilePhoto;
      const filePath = await this.filesService.saveFile(createReadStream(), filename, mimetype, encoding);
      user.profilePhoto = filename;
      return this.userRepository.save(user);
    }

    return user;
  }

public async updateCoverPic(userId: string, coverPhoto?: FileUpload): Promise<User> {
  const user = await this.userRepository.findOne({
    where: { id: parseInt(userId) }
  });

  if (!user) {
    throw new NotFoundException('User not found');
  }

  if (coverPhoto) {
    const { createReadStream, filename, mimetype, encoding } = coverPhoto;
    const filePath = await this.filesService.saveFile(createReadStream(), filename, mimetype, encoding);
    user.coverPhoto = filename;
    return this.userRepository.save(user);
  }

  return user;
}

async getProfilePicByName(filename: string): Promise<{ buffer: Buffer; mimetype: string } | null> {
  console.log(filename);

 let file=await this.filesService.getFile(filename)

  return file

}

async findFiltered(filter: UserFilterInput): Promise<{ users: User[], meta: PaginationMeta }> {
  const {
    search = '',
    roles = [],
    location = '',
    page = 1,
    limit = 10
  } = filter || {};

  const queryBuilder = this.userRepository.createQueryBuilder('user');

  if (search && search.trim() !== '') {
    queryBuilder.andWhere(
      '(user.username ILIKE :search OR user.email ILIKE :search OR user.bio ILIKE :search)',
      { search: `%${search}%` }
    );
  }

  if (location && location.trim() !== '') {
    queryBuilder.andWhere('user.location ILIKE :location', { location: `%${location}%` });
  }

  if (roles && Array.isArray(roles) && roles.length > 0) {
    // Convert role strings to lowercase to match enum values
    const normalizedRoles = roles.map(role => role.toLowerCase());
    queryBuilder.andWhere(
      'user.roles && ARRAY[:...roles]::user_roles_enum[]',
      { roles: normalizedRoles }
    );
  }

  const total = await queryBuilder.getCount();

  queryBuilder
    .orderBy('user.id', 'DESC')
    .skip((page - 1) * limit)
    .take(limit);

  const users = await queryBuilder.getMany();

  const meta = {
    totalItems: total,
    itemCount: users.length,
    itemsPerPage: limit,
    totalPages: Math.ceil(total / limit),
    currentPage: page
  };

  return { users, meta };
}

async getUserById(id: number): Promise<User | null> {
  return await this.userRepository.findOne({
    where: { id },
    select: [
      'id', 'email', 'username', 'roles', 'title', 'location',
      'skills', 'bio', 'coverPhoto', 'profilePhoto',
      'linkedin', 'github', 'website', 'x', 'mobile',
      'experiences', 'certifications', 'education'
    ]
  });
}

}


