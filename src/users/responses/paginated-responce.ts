import { ObjectType, Field, Int } from '@nestjs/graphql';
import { User } from '../../shared/entities/user.entity';

@ObjectType()
export class PaginationMeta {
  @Field(() => Int)
  totalItems: number;
  
  @Field(() => Int)
  itemCount: number;
  
  @Field(() => Int)
  itemsPerPage: number;
  
  @Field(() => Int)
  totalPages: number;
  
  @Field(() => Int)
  currentPage: number;
}

@ObjectType()
export class PaginatedUsers {
  @Field(() => [User])
  users: User[];
  
  @Field(() => PaginationMeta)
  meta: PaginationMeta;
}