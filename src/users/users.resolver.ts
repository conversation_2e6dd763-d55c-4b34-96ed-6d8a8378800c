// src/users/users.resolver.ts

import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { UsersService } from './users.service';
import { RoleEnum } from '../shared/enums/user.roles.enum';
import { User } from '../shared/entities/user.entity';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorator'
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { UseGuards } from '@nestjs/common';
import { UpdateProfileInput } from './dto/update-profile.input';
import { FileUpload, GraphQLUpload } from 'graphql-upload';
import { FileResponse } from '../shared/dtos/file-response.type';
import { PaginatedUsers } from './responses/paginated-responce';
import { UserFilterInput } from './dto/user-filter.input';
import { Int } from 'type-graphql';

@Resolver(() => User)
export class UsersResolver {
  constructor(private readonly usersService: UsersService) {}

  @Query(() => [User])
  @UseGuards(JwtAuthGuard, RolesGuard)  // Add both guards, order matters!
  @Roles(RoleEnum.USER)
  async users(): Promise<User[]> {
    return this.usersService.findAll();
  }

  @Mutation(() => User)
  @UseGuards(JwtAuthGuard)
  async updateProfile(
    @Context() context,
    @Args('input') input: UpdateProfileInput
  ): Promise<User> {
    const userId = context.req.user.id;
    return this.usersService.updateProfile(userId, input);
  }



  @Mutation(() => User)
  @UseGuards(JwtAuthGuard)
  async updateProfilepic(
    @Context() context,
    @Args({ name: 'profilePhoto', type: () => GraphQLUpload, nullable: true })
    profilePhoto?: FileUpload
  ): Promise<User> {
    const userId = context.req.user.id;
    return this.usersService.updateProfilepic(userId, profilePhoto);
  }

  @Mutation(() => User)
  @UseGuards(JwtAuthGuard)
  async updateCoverPic(
    @Context() context,
    @Args({ name: 'coverPhoto', type: () => GraphQLUpload, nullable: true })
    coverPhoto?: FileUpload
  ): Promise<User> {
    const userId = context.req.user.id;
    return this.usersService.updateCoverPic(userId, coverPhoto);
  }
 
  

  @Query(() => FileResponse, { nullable: true })
  async getProfilePicByName(
    @Args('filename') filename: string
  ): Promise<FileResponse | null> {
    const file = await this.usersService.getProfilePicByName(filename);
    if (!file) {
      return null;
    }
    
    return {
      base64Data: file.buffer.toString('base64'),
      mimetype: file.mimetype
    };
  }

  @Query(() => PaginatedUsers)
  async filteredUsers(
    @Args('search', { type: () => String, nullable: true }) search: string,
    @Args('roles', { type: () => [String], nullable: true }) roles: string[],
    @Args('location', { type: () => String, nullable: true }) location: string,
    @Args('page', { type: () => Int, nullable: true, defaultValue: 1 }) page: number,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 10 }) limit: number
  ): Promise<PaginatedUsers> {
    const filter = { search, roles, location, page, limit };
    console.log("Received filter in resolver:", filter);
    
    const result = await this.usersService.findFiltered(filter);
    
    return {
      users: result.users,
      meta: result.meta
    };
  }

  @Query(() => User, { nullable: true })
  async getUserById(
    @Args('id', { type: () => Int }) id: number
  ): Promise<User | null> {
    return this.usersService.getUserById(id);
  }
}