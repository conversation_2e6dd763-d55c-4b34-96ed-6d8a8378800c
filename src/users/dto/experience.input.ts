import { InputType, Field } from '@nestjs/graphql';
import { IsString, IsArray, IsOptional, MaxLength } from 'class-validator';

@InputType()
export class ExperienceInput {
  @Field()
  @IsString()
  @MaxLength(100)
  title: string;

  @Field()
  @IsString()
  @MaxLength(100)
  company: string;

  @Field()
  @IsString()
  @MaxLength(50)
  duration: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsArray()
  accomplishments?: string[];
}
