import { Field, InputType } from "@nestjs/graphql";
import { Int } from "type-graphql";

@InputType()
export class UserFilterInput {
  @Field(() => String, { nullable: true })
  search?: string;
  
  @Field(() => [String], { nullable: true })
  roles?: string[];

  @Field(() => String, { nullable: true })
  location?: string;

  @Field(() => Int, { nullable: true, defaultValue: 1 })
  page?: number;
  
  @Field(() => Int, { nullable: true, defaultValue: 10 })
  limit?: number;
}