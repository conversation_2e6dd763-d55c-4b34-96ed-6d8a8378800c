import { InputType, Field } from '@nestjs/graphql';
import { IsString, IsOptional, MaxLength } from 'class-validator';

@InputType()
export class EducationInput {
  @Field()
  @IsString()
  @MaxLength(100)
  institution: string;

  @Field()
  @IsString()
  @MaxLength(100)
  degree: string;

  @Field()
  @IsString()
  @MaxLength(100)
  fieldOfStudy: string;

  @Field()
  @IsString()
  startDate: string;

  @Field()
  @IsString()
  endDate: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;
}
