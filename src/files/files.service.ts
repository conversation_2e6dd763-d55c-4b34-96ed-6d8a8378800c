// src/files/files.service.ts

import { Injectable } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';
import * as uuid from 'uuid';

@Injectable()
export class FilesService {
  private readonly uploadsDir = path.join(process.cwd(), 'uploads');
  private readonly uploadproviderDir=path.join(process.cwd(), 'uploads','providers');

  constructor() {
    if (!fs.existsSync(this.uploadsDir)) {
      fs.mkdirSync(this.uploadsDir, { recursive: true });
    }
    if (!fs.existsSync(this.uploadproviderDir)) {
      fs.mkdirSync(this.uploadproviderDir, { recursive: true });
    }
  }

  async saveFile(
    readStream: NodeJS.ReadableStream,
    filename: string,
    mimetype: string,
    encoding: string,
  ): Promise<string> {
    const filePath = path.join(this.uploadsDir, `${uuid.v4()}-${filename}`);
    const writeStream = fs.createWriteStream(filePath);

    // Pipe the file stream to the local filesystem
    readStream.pipe(writeStream);

    return new Promise((resolve, reject) => {
      writeStream.on('finish', () => resolve(filePath));
      writeStream.on('error', (err) => reject(err));
    });
  }

  async getFile(filename: string): Promise<{ buffer: Buffer; mimetype: string } | null> {
    const files = await fs.promises.readdir(this.uploadsDir);
    const matchingFile = files.find(file => file.includes(filename));

    if (!matchingFile) {
      return null;
    }

    const filePath = path.join(this.uploadsDir, matchingFile);
    const buffer = await fs.promises.readFile(filePath);
    
    // Determine mimetype based on file extension
    const ext = path.extname(matchingFile).toLowerCase();
    const mimetype = ext === '.png' ? 'image/png' : 
                    ext === '.jpg' || ext === '.jpeg' ? 'image/jpeg' : 
                    ext === '.gif' ? 'image/gif' : 'application/octet-stream';

    return { buffer, mimetype };
  }
  
  async getProviderImage(providerId: number, filename: string): Promise<{ buffer: Buffer; mimetype: string } | null> {
    // Build the path to the provider's directory
    const providerDir = path.join(this.uploadproviderDir, providerId.toString());
    
    // Check if provider directory exists
    if (!fs.existsSync(providerDir)) {
      return null;
    }
    
    // Get all files in the provider directory
    const files = await fs.promises.readdir(providerDir);
    const matchingFile = files.find(file => file === filename);
    
    if (!matchingFile) {
      return null;
    }
    
    const filePath = path.join(providerDir, matchingFile);
    const buffer = await fs.promises.readFile(filePath);
    
    // Determine mimetype based on file extension
    const ext = path.extname(matchingFile).toLowerCase();
    const mimetype = ext === '.png' ? 'image/png' : 
                    ext === '.jpg' || ext === '.jpeg' ? 'image/jpeg' : 
                    ext === '.gif' ? 'image/gif' : 'application/octet-stream';
    
    return { buffer, mimetype };
  }

  async saveProvidersimage(
    readStream: NodeJS.ReadableStream,
    filename: string,
    mimetype: string,
    encoding: string,
    id: number
  ): Promise<string> {
    // Create provider-specific directory if it doesn't exist
    const providerDir = path.join(this.uploadproviderDir, id.toString());
    if (!fs.existsSync(providerDir)) {
      fs.mkdirSync(providerDir, { recursive: true });
    }
    
    // Save file in the provider-specific directory
    const filePath = path.join(providerDir, `${filename}`);
    const writeStream = fs.createWriteStream(filePath);

    // Pipe the file stream to the local filesystem
    readStream.pipe(writeStream);

    return new Promise((resolve, reject) => {
      writeStream.on('finish', () => resolve(filePath));
      writeStream.on('error', (err) => reject(err));
    });
  }



}