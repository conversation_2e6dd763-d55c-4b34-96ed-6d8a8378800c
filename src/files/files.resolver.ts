// src/files/files.resolver.ts

import { Resolver, Mutation, Args } from '@nestjs/graphql';
import { FileUpload } from 'graphql-upload';
import { GraphQLUpload } from 'graphql-upload';
import { FilesService } from './files.service';

@Resolver('File')
export class FilesResolver {
  constructor(private readonly filesService: FilesService) {}

  @Mutation(() => String)
  async uploadFile(
    @Args({ name: 'file', type: () => GraphQLUpload })
    file: Promise<FileUpload>
  ): Promise<string> {
    const { createReadStream, filename, mimetype, encoding } = await file;
    return this.filesService.saveFile(createReadStream(), filename, mimetype, encoding);
  }

  @Mutation(() => [String])
  async uploadMultipleFiles(
    @Args({ name: 'files', type: () => [GraphQLUpload] })
    files: Promise<FileUpload>[]
  ): Promise<string[]> {
    const results = await Promise.all(
      files.map(async (file) => {
        const { createReadStream, filename, mimetype, encoding } = await file;
        return this.filesService.saveFile(createReadStream(), filename, mimetype, encoding);
      })
    );
    return results;
  }
}