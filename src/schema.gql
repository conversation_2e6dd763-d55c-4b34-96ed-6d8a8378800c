# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type AdminStats {
  enrollments: EnrollmentStats!
  providers: ProviderStats!
  trainingCategories: [CategoryDistribution!]!
  trainings: TrainingStats!
  users: UserStats!
}

type CategoryDistribution {
  category: String!
  count: Int!
}

type Certification {
  name: String!
  url: String
}

input CertificationInput {
  name: String!
  url: String
}

input CreateProviderInput {
  address: String!
  category: String
  city: String!
  country: String!
  degreeTypes: [String!]
  description: String
  email: String!
  imageUrls: [String!]
  languages: [String!]
  linkedin: String
  logoUrl: String
  name: String!
  ownership: String!
  phone: String
  postalCode: String
  state: String
  type: String!
  website: String
  x: String
  yearFounded: Float
}

input CreateProviderUserInput {
  email: String!
  location: String
  mobile: String
  title: String
  username: String!
}

type CreateProviderUserResponse {
  error: String
  generatedPassword: String
  message: String!
  statusCode: Float!
  user: User
}

input CreateTrainingInput {
  category: String!
  certificate: Boolean!
  description: String!
  duration: String!
  format: String!
  image: String
  instructor: InstructorInput!
  labs: [LabInput!]!
  language: String!
  level: String!
  location: String
  modules: [ModuleInput!]!
  price: Float!
  provider: String
  providerId: Int!
  requirements: [String!]!
  resources: [ResourceInput!]!
  startDate: Timestamp!
  title: String!
}

"""
A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.This scalar is serialized to a string in ISO 8601 format and parsed from a string in ISO 8601 format.
"""
scalar DateTimeISO

type Education {
  degree: String!
  description: String
  endDate: String!
  fieldOfStudy: String!
  institution: String!
  startDate: String!
}

input EducationInput {
  degree: String!
  description: String
  endDate: String!
  fieldOfStudy: String!
  institution: String!
  startDate: String!
}

type Enrollment {
  enrolledAt: Timestamp!
  id: ID!
  status: String!
  training: Training!
  trainingId: ID!
  user: User!
  userId: ID!
}

type EnrollmentPaginationMeta {
  currentPage: Int!
  itemCount: Int!
  itemsPerPage: Int!
  totalItems: Int!
  totalPages: Int!
}

type EnrollmentStats {
  newEnrollmentsThisMonth: Int!
  newEnrollmentsThisWeek: Int!
  newEnrollmentsToday: Int!
  total: Int!
  totalRevenue: Float!
}

type Experience {
  accomplishments: [String!]
  company: String!
  description: String
  duration: String!
  title: String!
}

input ExperienceInput {
  accomplishments: [String!]
  company: String!
  description: String
  duration: String!
  title: String!
}

type FileResponse {
  base64Data: String!
  mimetype: String!
}

input ForgotPasswordInput {
  email: String!
}

type ForgotPasswordResponse {
  error: String
  message: String!
  statusCode: Float!
  success: Boolean!
}

type Instructor {
  email: String!
  name: String!
  specialization: String!
}

input InstructorInput {
  email: String!
  name: String!
  specialization: String!
}

type Lab {
  description: String!
  title: String!
}

input LabInput {
  description: String!
  title: String!
}

input LoginInput {
  email: String!
  password: String!
}

type LoginResponse {
  error: String
  message: String
  statusCode: Float
  token: String
  user: User
}

type Module {
  description: String!
  title: String!
}

input ModuleInput {
  description: String!
  title: String!
}

type Mutation {
  addProviderImage(id: Int!, image: Upload!): Provider!
  cancelEnrollment(trainingId: Int!): Boolean!
  createProvider(input: CreateProviderInput!, logo: Upload): Provider!
  createProviderUser(input: CreateProviderUserInput!): CreateProviderUserResponse!
  createTraining(image: Upload, input: CreateTrainingInput!): Training!
  deleteTraining(id: Int!): Boolean!
  enrollInTraining(trainingId: Int!): Enrollment!
  forgotPassword(input: ForgotPasswordInput!): ForgotPasswordResponse!
  login(input: LoginInput!): LoginResponse!
  register(input: RegisterInput!): RegisterResponse!
  removeProvider(id: Int!): Boolean!
  removeProviderImage(id: Int!, imageUrl: String!): Provider!
  resetPassword(input: ResetPasswordInput!): ResetPasswordResponse!
  sendWelcomeEmailTest(email: String!, username: String!): Boolean!
  testSendEmail(email: String!, subject: String, text: String): Boolean!
  updateCoverPic(coverPhoto: Upload): User!
  updateEnrollment(input: UpdateEnrollmentInput!): Enrollment!
  updateProfile(input: UpdateProfileInput!): User!
  updateProfilepic(profilePhoto: Upload): User!
  updateProvider(id: Int!, images: [Upload!], input: UpdateProviderInput!, logo: Upload): Provider!
  updateTraining(image: Upload, input: UpdateTrainingInput!): Training!
  uploadFile(file: Upload!): String!
  uploadMultipleFiles(files: [Upload!]!): [String!]!
  verifyEmail(token: String!): VerifyEmailResponse!
}

type PaginatedEnrollments {
  enrollments: [Enrollment!]!
  meta: EnrollmentPaginationMeta!
}

type PaginatedProviders {
  meta: ProviderPaginationMeta!
  providers: [Provider!]!
}

type PaginatedTrainings {
  meta: TrainingPaginationMeta!
  trainings: [Training!]!
}

type PaginatedUsers {
  meta: PaginationMeta!
  users: [User!]!
}

type PaginationMeta {
  currentPage: Int!
  itemCount: Int!
  itemsPerPage: Int!
  totalItems: Int!
  totalPages: Int!
}

type Provider {
  address: String!
  category: String
  city: String!
  country: String!
  createdAt: Timestamp!
  createdBy: User
  createdById: ID
  degreeTypes: [String!]
  description: String
  email: String!
  id: ID!
  imageUrls: [String!]
  languages: [String!]
  linkedin: String
  logoUrl: String
  name: String!
  ownership: String!
  phone: String
  postalCode: String
  state: String
  type: String!
  updatedAt: Timestamp!
  website: String
  x: String
  yearFounded: Float
}

input ProviderFilterInput {
  category: String
  city: String
  country: String
  limit: Int = 10
  ownership: String
  page: Int = 1
  search: String
  state: String
  type: String
}

type ProviderPaginationMeta {
  currentPage: Float!
  itemCount: Float!
  itemsPerPage: Float!
  totalItems: Float!
  totalPages: Float!
}

type ProviderStats {
  newProvidersThisMonth: Int!
  newProvidersThisWeek: Int!
  newProvidersToday: Int!
  total: Int!
}

type Query {
  adminStats(timePeriod: TimePeriodInput): AdminStats!
  filteredProviders(filter: ProviderFilterInput): PaginatedProviders!
  filteredTrainings(category: String, certificate: Boolean, format: String, level: String, limit: Int = 10, location: String, maxPrice: Float, minPrice: Float, page: Int = 1, search: String): PaginatedTrainings!
  filteredUsers(limit: Int = 10, location: String, page: Int = 1, roles: [String!], search: String): PaginatedUsers!
  getLogoProvider(filename: String!): FileResponse
  getMyProviders(category: String, city: String, country: String, limit: Int = 10, ownership: String, page: Int = 1, search: String, state: String, type: String): PaginatedProviders!
  getProfile: User!
  getProfilePicByName(filename: String!): FileResponse
  getProviderImage(filename: String!, providerId: Int!): FileResponse
  getProviders(category: String, city: String, country: String, limit: Int = 10, ownership: String, page: Int = 1, search: String, state: String, type: String): PaginatedProviders!
  getTrainingById(id: Int!): Training
  getTrainingEnrollments(trainingId: Int!): [Enrollment!]!
  getTrainingImageByName(filename: String!): FileResponse
  getUserById(id: Int!): User
  hello: String!
  helloWorld: String!
  myEnrollments: [Enrollment!]!
  myProviders(filter: ProviderFilterInput): PaginatedProviders!
  myTrainings(category: String, certificate: Boolean, format: String, level: String, limit: Int = 10, location: String, maxPrice: Float, minPrice: Float, page: Int = 1, search: String): PaginatedTrainings!
  myTrainingsWithFilter(filter: TrainingFilterInput): PaginatedTrainings!
  provider(id: Int!): Provider!
  providers: [Provider!]!
  trainingEnrollments(limit: Int = 10, page: Int = 1, search: String, status: String, trainingId: Int): PaginatedEnrollments!
  trainings: [Training!]!
  users: [User!]!
}

input RegisterInput {
  email: String!
  password: String!
  username: String!
}

type RegisterResponse {
  error: String
  message: String!
  statusCode: Float
  user: User
}

input ResetPasswordInput {
  newPassword: String!
  token: String!
}

type ResetPasswordResponse {
  error: String
  message: String!
  statusCode: Float!
  success: Boolean!
}

type Resource {
  description: String!
  name: String!
}

input ResourceInput {
  description: String!
  name: String!
}

input TimePeriodInput {
  endDate: Timestamp
  startDate: Timestamp
}

"""
`Date` type as integer. Type represents date and time as number of milliseconds from start of UNIX epoch.
"""
scalar Timestamp

type Training {
  category: String!
  certificate: Boolean!
  createdAt: Timestamp!
  createdBy: ID
  description: String!
  duration: String!
  enrolledUserIds: [Float!]!
  enrollments: Float!
  format: String!
  id: Float!
  image: String!
  instructor: Instructor!
  labs: [Lab!]!
  language: String!
  level: String!
  location: String
  modules: [Module!]!
  price: Float!
  provider: String!
  providerEntity: Provider
  providerId: ID
  rating: Float!
  requirements: [String!]!
  resources: [Resource!]!
  reviews: Float
  startDate: DateTimeISO
  title: String!
  updatedAt: Timestamp!
}

input TrainingFilterInput {
  category: String
  certificate: Boolean
  format: String
  level: String
  limit: Int = 10
  location: String
  maxPrice: Float
  minPrice: Float
  page: Int = 1
  search: String
}

type TrainingPaginationMeta {
  currentPage: Int!
  itemCount: Int!
  itemsPerPage: Int!
  totalItems: Int!
  totalPages: Int!
}

type TrainingStats {
  averagePrice: Float!
  newTrainingsThisMonth: Int!
  newTrainingsThisWeek: Int!
  newTrainingsToday: Int!
  total: Int!
}

input UpdateEnrollmentInput {
  id: Int!
  status: String!
}

input UpdateProfileInput {
  bio: String
  certifications: [CertificationInput!]
  education: [EducationInput!]
  experiences: [ExperienceInput!]
  github: String
  linkedin: String
  location: String
  skills: [String!]
  title: String
  username: String
  website: String
  x: String
}

input UpdateProviderInput {
  address: String
  category: String
  city: String
  country: String
  degreeTypes: [String!]
  description: String
  email: String
  existingImages: [String!]
  id: ID!
  imageUrls: [String!]
  languages: [String!]
  linkedin: String
  logoUrl: String
  name: String
  ownership: String
  phone: String
  postalCode: String
  removeImages: [String!]
  state: String
  type: String
  website: String
  x: String
  yearFounded: Float
}

input UpdateTrainingInput {
  category: String
  certificate: Boolean
  description: String
  duration: String
  format: String
  id: Int!
  image: String
  instructor: InstructorInput
  labs: [LabInput!]
  language: String
  level: String
  location: String
  modules: [ModuleInput!]
  price: Float
  provider: String
  providerId: Int
  requirements: [String!]
  resources: [ResourceInput!]
  startDate: Timestamp
  title: String
}

"""The `Upload` scalar type represents a file upload."""
scalar Upload

type User {
  bio: String
  certifications: [Certification!]
  coverPhoto: String
  education: [Education!]
  email: String!
  experiences: [Experience!]
  github: String
  id: ID!
  linkedin: String
  location: String
  mobile: String
  profilePhoto: String
  roles: [String!]!
  skills: [String!]
  title: String
  username: String
  website: String
  x: String
}

type UserRoleDistribution {
  count: Int!
  role: String!
}

type UserStats {
  newUsersThisMonth: Int!
  newUsersThisWeek: Int!
  newUsersToday: Int!
  roleDistribution: [UserRoleDistribution!]!
  total: Int!
}

type VerifyEmailResponse {
  message: String!
  statusCode: Float
  success: Boolean!
}