import { GraphQLError } from 'graphql';

export class AuthError extends GraphQLError {
  constructor(message: string, code: string, details?: Record<string, any>) {
    super(message, {
      extensions: {
        code,
        timestamp: new Date().toISOString(),
        details
      }
    });
  }
}

export class InvalidCredentialsError extends AuthError {
  constructor(email: string) {
    super(
      'Invalid credentials',
      'INVALID_CREDENTIALS',
      {
        email,
        message: 'The email or password is incorrect',
        suggestion: 'Please check your credentials and try again'
      }
    );
  }
}

export class UserExistsError extends AuthError {
  constructor(email: string) {
    super(
      'User already exists',
      'USER_EXISTS',
      {
        email,
        message: 'An account with this email already exists',
        suggestion: 'Please try logging in or use a different email'
      }
    );
  }
}