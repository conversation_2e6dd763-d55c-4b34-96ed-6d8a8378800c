import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { ObjectType, Field, ID, Float } from '@nestjs/graphql';
import { ObjectType as TypeGraphQLObjectType, Field as TypeGraphQLField, ID as TypeGraphQLID, Float as TypeGraphQLFloat, GraphQLISODateTime } from 'type-graphql';
import { Provider } from './provider.entity';

@TypeGraphQLObjectType()
@ObjectType()
class Instructor {
  @TypeGraphQLField()
  @Field()
  name: string;

  @TypeGraphQLField()
  @Field()
  email: string;

  @TypeGraphQLField()
  @Field()
  specialization: string;
}

@TypeGraphQLObjectType()
@ObjectType()
class Module {
  @TypeGraphQLField()
  @Field()
  title: string;

  @TypeGraphQLField()
  @Field()
  description: string;
}

@TypeGraphQLObjectType()
@ObjectType()
class Lab {
  @TypeGraphQLField()
  @Field()
  title: string;

  @TypeGraphQLField()
  @Field()
  description: string;
}

@TypeGraphQLObjectType()
@ObjectType()
class Resource {
  @TypeGraphQLField()
  @Field()
  name: string;

  @TypeGraphQLField()
  @Field()
  description: string;
}

@TypeGraphQLObjectType()
@ObjectType()
@Entity()
export class Training {
  @TypeGraphQLField(() => TypeGraphQLID)
  @Field()
  @PrimaryGeneratedColumn()
  id: number;

  @TypeGraphQLField()
  @Field()
  @Column()
  title: string;

  // Keep the existing provider field temporarily
  @TypeGraphQLField()
  @Field()
  @Column()
  provider: string;

  // Make providerId nullable initially
  @TypeGraphQLField(() => TypeGraphQLID, { nullable: true })
  @Field(() => ID, { nullable: true })
  @Column({ nullable: true })
  providerId: number;

  // Add proper relation to Provider entity
  @ManyToOne(() => Provider)
  @JoinColumn({ name: 'providerId' })
  @Field(() => Provider, { nullable: true })
  providerEntity: Provider;

  // Add createdBy field
  @TypeGraphQLField(() => TypeGraphQLID, { nullable: true })
  @Field(() => ID, { nullable: true })
  @Column({ nullable: true })
  createdBy: number;

  @TypeGraphQLField(() => TypeGraphQLFloat)
  @Field()
  @Column('float')
  price: number;

  @TypeGraphQLField(() => TypeGraphQLFloat)
  @Field()
  @Column('float', { default: 0 })
  rating: number;

  @TypeGraphQLField()
  @Field()
  @Column({ default: 0 })
  enrollments: number;

  @TypeGraphQLField({ nullable: true })
  @Field({ nullable: true })
  @Column({ nullable: true })
  reviews?: number;

  @TypeGraphQLField()
  @Field()
  @Column()
  duration: string;

  @TypeGraphQLField()
  @Field()
  @Column()
  level: string;

  @TypeGraphQLField()
  @Field()
  @Column()
  format: string;

  @TypeGraphQLField()
  @Field()
  @Column()
  category: string;

  @Column({
    nullable: true,
    type: 'timestamp'
})
@Field(() => GraphQLISODateTime, {nullable: true})
  startDate: Date;

  @TypeGraphQLField()
  @Field()
  @Column()
  description: string;

  @TypeGraphQLField()
  @Field()
  @Column()
  image: string;

  @TypeGraphQLField(() => Instructor)
  @Field(() => Instructor)
  @Column('jsonb')
  instructor: Instructor;

  @TypeGraphQLField(() => [String])
  @Field(() => [String])
  @Column('text', { array: true })
  requirements: string[];

  @TypeGraphQLField()
  @Field()
  @Column()
  language: string;

  @TypeGraphQLField()
  @Field()
  @Column()
  certificate: boolean;

  @TypeGraphQLField(() => [Module])
  @Field(() => [Module])
  @Column('jsonb')
  modules: Module[];

  @TypeGraphQLField(() => [Lab])
  @Field(() => [Lab])
  @Column('jsonb')
  labs: Lab[];

  @TypeGraphQLField(() => [Resource])
  @Field(() => [Resource])
  @Column('jsonb')
  resources: Resource[];

  @TypeGraphQLField({ nullable: true })
  @Field({ nullable: true })
  @Column({ nullable: true })
  location?: string;

  @TypeGraphQLField(() => [Number])
  @Field(() => [Number])
  @Column('int', { array: true, default: [], nullable: true })
  enrolledUserIds: number[];

  @TypeGraphQLField(() => GraphQLISODateTime)
  @Field(() => Date)
  @CreateDateColumn()
  createdAt: Date;

  @TypeGraphQLField(() => GraphQLISODateTime)
  @Field(() => Date)
  @UpdateDateColumn()
  updatedAt: Date;
}