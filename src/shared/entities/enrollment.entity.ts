import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON>n, CreateDateColumn } from 'typeorm';
import { ObjectType, Field, ID } from '@nestjs/graphql';
import { User } from './user.entity';
import { Training } from './training.entity';

@ObjectType()
@Entity()
export class Enrollment {
  @Field(() => ID)
  @PrimaryGeneratedColumn()
  id: number;

  @Field(() => ID)
  @Column()
  userId: number;

  @Field(() => ID)
  @Column()
  trainingId: number;

  @Field(() => User)
  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Field(() => Training)
  @ManyToOne(() => Training)
  @JoinColumn({ name: 'trainingId' })
  training: Training;

  @Field()
  @CreateDateColumn()
  enrolledAt: Date;

  @Field({ defaultValue: 'PENDING' })
  @Column({ default: 'PENDING' })
  status: string;

  
}