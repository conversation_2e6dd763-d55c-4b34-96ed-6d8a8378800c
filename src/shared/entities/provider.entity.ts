import { ObjectType, Field, ID } from '@nestjs/graphql';
import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from './user.entity';

@ObjectType()
@Entity('provider')
export class Provider {
  @Field(() => ID)
  @PrimaryGeneratedColumn()
  id: number;

  @Field()
  @Column()
  name: string;

  @Field()
  @Column()
  city: string;

  @Field()
  @Column()
  country: string;

  @Field({ nullable: true })
  @Column({ nullable: true })
  state: string;

  @Field({ nullable: true })
  @Column({ nullable: true })
  postalCode: string;

  @Field()
  @Column()
  address: string;

  @Field({ nullable: true })
  @Column({ nullable: true, type: 'text' })
  description: string;

  @Field({ nullable: true })
  @Column({ nullable: true })
  yearFounded: number;

  @Field()
  @Column({
    type: 'enum',
    enum: ['university', 'college', 'high_school', 'elementary', 'middle_school', 'vocational', 'language_school', 'other'],
    default: 'university'
  })
  type: string;

  @Field({ nullable: true })
  @Column({ nullable: true })
  website: string;

  @Field()
  @Column()
  email: string;

  @Field({ nullable: true })
  @Column({ nullable: true })
  phone: string;

  @Field({ nullable: true })
  @Column({ nullable: true })
  linkedin: string;

  @Field({ nullable: true })
  @Column({ nullable: true })
  x: string;

  @Field()
  @Column({
    type: 'enum',
    enum: ['public', 'private', 'charter', 'international'],
    default: 'public'
  })
  ownership: string;

  @Field({ nullable: true })
  @Column({ nullable: true })
  category: string;

  @Field(() => [String], { nullable: true })
  @Column('simple-array', { nullable: true })
  languages: string[];

  @Field(() => [String], { nullable: true })
  @Column('simple-array', { nullable: true })
  degreeTypes: string[];

  @Field({ nullable: true })
  @Column({ nullable: true })
  logoUrl: string;

  @Field(() => [String], { nullable: true })
  @Column('simple-array', { nullable: true })
  imageUrls: string[];

  @Field()
  @CreateDateColumn()
  createdAt: Date;

  @Field()
  @UpdateDateColumn()
  updatedAt: Date;

  @Field(() => ID, { nullable: true })
  @Column({ nullable: true })
  createdById: number;

  @Field(() => User, { nullable: true })
  @ManyToOne(() => User)
  @JoinColumn({ name: 'createdById' })
  createdBy: User;
}