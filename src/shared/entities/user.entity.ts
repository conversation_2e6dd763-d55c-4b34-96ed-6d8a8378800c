// src/auth/entities/user.entity.ts

import { ObjectType, Field, ID } from '@nestjs/graphql';
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { RoleEnum } from '../enums/user.roles.enum';
import { Experience } from '../dto/experience.type';
import { Certification } from '../dto/certification.type';
import { Education } from '../dto/education.type';

@ObjectType()
@Entity('user')
export class User {
  @Field(() => ID)
  @PrimaryGeneratedColumn()
  id: number;

  @Field(() => String, { nullable: true })
  @Column({ nullable: true, unique: false })
  username?: string;

  @Field()
  @Column({ unique: true })
  email: string;

  @Column()
  password: string;

  @Field(() => [String])
  @Column({
    type: 'enum',
    enum: RoleEnum,
    array: true,
    default: [RoleEnum.USER]
  })
  roles: RoleEnum[];

    @Field({ nullable: true })
    @Column({ nullable: true })
    coverPhoto: string;

    @Field({ nullable: true })
    @Column({ nullable: true })
    profilePhoto: string;

   // Added new fields from profile data
   @Field({ nullable: true })
   @Column({ nullable: true })
   title: string;

   @Field({ nullable: true })
   @Column({ nullable: true })
   location: string;

   @Field(() => [String], { nullable: true })
   @Column('simple-array', { nullable: true })
   skills: string[];

   @Field({ nullable: true })
   @Column({ nullable: true, type: 'text' })
   bio: string;

    @Field({ nullable: true })
    @Column({ nullable: true })
    linkedin: string;

    @Field({ nullable: true })
    @Column({ nullable: true })
    github: string;

    @Field({ nullable: true })
    @Column({ nullable: true })
    x: string;

    @Field({ nullable: true })
    @Column({ nullable: true })
    website: string;

    @Field({ nullable: true })
    @Column({ nullable: true })
    mobile: string;

    @Field(() => [Experience], { nullable: true })
    @Column('jsonb', { nullable: true, default: [] })
    experiences: Experience[];

    @Field(() => [Certification], { nullable: true })
    @Column('jsonb', { nullable: true, default: [] })
    certifications: Certification[];

    @Field(() => [Education], { nullable: true })
    @Column('jsonb', { nullable: true, default: [] })
    education: Education[];
}