import { InputType, Field, ID } from '@nestjs/graphql';
import { IsString, IsEmail, IsOptional, IsUrl, IsNumber, IsEnum, IsArray, MaxLength } from 'class-validator';

@InputType()
export class UpdateProviderInput {
  @Field(() => ID)
  id: number;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  name?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  city?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  country?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  state?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  postalCode?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  address?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  description?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsNumber()
  yearFounded?: number;

  @Field({ nullable: true })
  @IsOptional()
  @IsEnum(['university', 'college', 'high_school', 'elementary', 'middle_school', 'vocational', 'language_school', 'other'])
  type?: string;

  @Field({ nullable: true })
  @IsOptional()
  website?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsEmail()
  email?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  phone?: string;

  @Field({ nullable: true })
  @IsOptional()
  linkedin?: string;

  @Field({ nullable: true })
  @IsOptional()
  x?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsEnum(['public', 'private', 'charter', 'international'])
  ownership?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  category?: string;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsArray()
  languages?: string[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsArray()
  degreeTypes?: string[];

  @Field({ nullable: true })
  @IsOptional()
  @IsUrl()
  logoUrl?: string;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsArray()
  imageUrls?: string[];
  
  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsArray()
  existingImages?: string[];
  
  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsArray()
  removeImages?: string[];
}
