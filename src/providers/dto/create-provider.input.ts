import { InputType, Field } from '@nestjs/graphql';
import { IsString, IsEmail, IsOptional, IsUrl, IsNumber, IsEnum, IsArray, MaxLength, ValidateIf } from 'class-validator';

@InputType()
export class CreateProviderInput {
  @Field()
  @IsString()
  @MaxLength(100)
  name: string;

  @Field()
  @IsString()
  city: string;

  @Field()
  @IsString()
  country: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  state?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  postalCode?: string;

  @Field()
  @IsString()
  address: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  description?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsNumber()
  yearFounded?: number;

  @Field()
  @IsEnum(['university', 'college', 'high_school', 'elementary', 'middle_school', 'vocational', 'language_school', 'other'])
  type: string;

  @Field({ nullable: true })
  @IsOptional()
  @ValidateIf((o) => o.website !== null && o.website !== undefined && o.website !== '')
  @IsUrl({ require_protocol: true }, { message: 'If provided, website must be a valid URL address (e.g., https://example.com)' })
  website?: string;

  @Field()
  @IsEmail()
  email: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  phone?: string;

  @Field({ nullable: true })
  @IsOptional()
  @ValidateIf((o) => o.linkedin !== null && o.linkedin !== undefined && o.linkedin !== '')
  @IsUrl({ require_protocol: true }, { message: 'If provided, linkedin must be a valid URL address (e.g., https://linkedin.com/in/username)' })
  linkedin?: string;

  @Field({ nullable: true })
  @IsOptional()
  @ValidateIf((o) => o.x !== null && o.x !== undefined && o.x !== '')
  @IsUrl({ require_protocol: true }, { message: 'If provided, x must be a valid URL address (e.g., https://x.com/username)' })
  x?: string;

  @Field()
  @IsEnum(['public', 'private', 'charter', 'international'])
  ownership: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  category?: string;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsArray()
  languages?: string[];

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsArray()
  degreeTypes?: string[];

  @Field({ nullable: true })
  @IsOptional()
  @ValidateIf((o) => o.logoUrl !== null && o.logoUrl !== undefined && o.logoUrl !== '')
  @IsUrl({ require_protocol: true }, { message: 'If provided, logoUrl must be a valid URL address' })
  logoUrl?: string;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsArray()
  imageUrls?: string[];
}