import { Field, InputType } from "@nestjs/graphql";
import { Int } from "type-graphql";

@InputType()
export class ProviderFilterInput {
  @Field(() => String, { nullable: true })
  search?: string;
  
  @Field(() => String, { nullable: true })
  type?: string;
  
  @Field(() => String, { nullable: true })
  ownership?: string;
  
  @Field(() => String, { nullable: true })
  country?: string;

  @Field(() => String, { nullable: true })
  state?: string;
  
  @Field(() => String, { nullable: true })
  city?: string;
  
  @Field(() => String, { nullable: true })
  category?: string;
  
  @Field(() => Int, { nullable: true, defaultValue: 1 })
  page?: number;
  
  @Field(() => Int, { nullable: true, defaultValue: 10 })
  limit?: number;
}