import { Resolver, Query, Mutation, Args, Int, Context } from '@nestjs/graphql';
import { ProvidersService } from './providers.service';
import { Provider } from '../shared/entities/provider.entity';
import { CreateProviderInput } from './dto/create-provider.input';
import { UpdateProviderInput } from './dto/update-provider.input';
import { ProviderFilterInput } from './dto/provider-filter.input';
import { PaginatedProviders } from './responses/paginated-providers';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorator';
import { RoleEnum } from '../shared/enums/user.roles.enum';
import { FileUpload, GraphQLUpload } from 'graphql-upload';

import { FileResponse } from '../shared/dtos/file-response.type';

@Resolver(() => Provider)
export class ProvidersResolver {
  constructor(private readonly providersService: ProvidersService) {}

  @Query(() => [Provider])
  async providers(): Promise<Provider[]> {
    return this.providersService.findAll();
  }

  @Query(() => Provider)
  async provider(@Args('id', { type: () => Int }) id: number): Promise<Provider> {
    return this.providersService.findOne(id);
  }

  // Keep the original method for backward compatibility
  @Query(() => PaginatedProviders)
  async filteredProviders(
    @Args('filter', { nullable: true }) filter?: ProviderFilterInput
  ): Promise<PaginatedProviders> {
    return this.providersService.getFilteredProviders(filter);
  }

  // New query with individual parameters
  @Query(() => PaginatedProviders, { name: 'getProviders' })
  async getProvidersWithFilters(
    @Args('search', { type: () => String, nullable: true }) search?: string,
    @Args('type', { type: () => String, nullable: true }) type?: string,
    @Args('ownership', { type: () => String, nullable: true }) ownership?: string,
    @Args('country', { type: () => String, nullable: true }) country?: string,
    @Args('state', { type: () => String, nullable: true }) state?: string,
    @Args('city', { type: () => String, nullable: true }) city?: string,
    @Args('category', { type: () => String, nullable: true }) category?: string,
    @Args('page', { type: () => Int, nullable: true, defaultValue: 1 }) page?: number,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 10 }) limit?: number
  ): Promise<PaginatedProviders> {
    // Create filter object from individual parameters
    const filter: ProviderFilterInput = {
      search,
      type,
      ownership,
      country,
      state,
      city,
      category,
      page,
      limit
    };

    return this.providersService.getFilteredProviders(filter);
  }

  @Query(() => PaginatedProviders, { name: 'myProviders' })
  @UseGuards(JwtAuthGuard)
  async getMyProviders(
    @Context() context: { req: { user: { id: number } } },
    @Args('filter', { nullable: true }) filter?: ProviderFilterInput
  ): Promise<PaginatedProviders> {
    const userId = context.req.user.id;
    return this.providersService.getMyProviders(userId, filter);
  }

  @Query(() => PaginatedProviders, { name: 'getMyProviders' })
  @UseGuards(JwtAuthGuard)
  async getMyProvidersWithFilters(
    @Context() context: { req: { user: { id: number } } },
    @Args('search', { type: () => String, nullable: true }) search?: string,
    @Args('type', { type: () => String, nullable: true }) type?: string,
    @Args('ownership', { type: () => String, nullable: true }) ownership?: string,
    @Args('country', { type: () => String, nullable: true }) country?: string,
    @Args('state', { type: () => String, nullable: true }) state?: string,
    @Args('city', { type: () => String, nullable: true }) city?: string,
    @Args('category', { type: () => String, nullable: true }) category?: string,
    @Args('page', { type: () => Int, nullable: true, defaultValue: 1 }) page?: number,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 10 }) limit?: number
  ): Promise<PaginatedProviders> {
    const userId = context.req.user.id;

    // Create filter object from individual parameters
    const filter: ProviderFilterInput = {
      search,
      type,
      ownership,
      country,
      state,
      city,
      category,
      page,
      limit
    };

    return this.providersService.getMyProviders(userId, filter);
  }

  @Mutation(() => Provider)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RoleEnum.USER)
  async createProvider(
    @Context() context: { req: { user: { id: number } } },
    @Args('input') input: CreateProviderInput,
    @Args({ name: 'logo', type: () => GraphQLUpload, nullable: true }) logo?: FileUpload
  ): Promise<Provider> {
    const userId = context.req.user.id;
    return this.providersService.create(input, logo, userId);
  }

  @Query(() => FileResponse, { nullable: true })
  async getLogoProvider(
    @Args('filename') filename: string
  ): Promise<FileResponse | null> {
    const file = await this.providersService.getLogoProvider(filename)
    if (!file) {
      return null;
    }

    return {
      base64Data: file.buffer.toString('base64'),
      mimetype: file.mimetype
    };
  }

  @Query(() => FileResponse, { nullable: true })
  async getProviderImage(
    @Args('providerId', { type: () => Int }) providerId: number,
    @Args('filename') filename: string
  ): Promise<FileResponse | null> {
    const file = await this.providersService.getProviderImage(providerId, filename);

    if (!file) {
      return null;
    }

    return {
      base64Data: file.buffer.toString('base64'),
      mimetype: file.mimetype
    };
  }

  // Replace the commented-out method and the non-decorated method with this:
  @Mutation(() => Provider)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RoleEnum.USER)
  async updateProvider(
    @Args('id', { type: () => Int }) id: number,
    @Args('input') input: UpdateProviderInput,
    @Args({ name: 'logo', type: () => GraphQLUpload, nullable: true }) logo?: FileUpload,
    @Args({ name: 'images', type: () => [GraphQLUpload], nullable: true }) images?: FileUpload[]
  ): Promise<Provider> {
    console.log("Updating provider with ID:", id);
    console.log("Input data:", JSON.stringify(input));

    // Make sure it's finding the existing provider by ID first
    const existingProvider = await this.providersService.findOne(id);

    if (!existingProvider) {
      throw new Error(`Provider with ID ${id} not found`);
    }

    // Then update it instead of creating a new one
    return this.providersService.update(id, input, logo, images);
  }

  @Mutation(() => Boolean)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RoleEnum.ADMIN)
  async removeProvider(@Args('id', { type: () => Int }) id: number): Promise<boolean> {
    return this.providersService.remove(id);
  }

  @Mutation(() => Provider)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RoleEnum.ADMIN)
  async addProviderImage(
    @Args('id', { type: () => Int }) id: number,
    @Args({ name: 'image', type: () => GraphQLUpload }) image: FileUpload
  ): Promise<Provider> {
    return this.providersService.addImage(id, image);
  }

  @Mutation(() => Provider)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RoleEnum.USER)
  async removeProviderImage(
    @Args('id', { type: () => Int }) id: number,
    @Args('imageUrl') imageUrl: string
  ): Promise<Provider> {
    return this.providersService.removeImage(id, imageUrl);
  }
}