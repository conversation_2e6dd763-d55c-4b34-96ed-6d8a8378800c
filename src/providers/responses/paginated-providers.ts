import { ObjectType, Field } from '@nestjs/graphql';
import { Provider } from '../../shared/entities/provider.entity';

@ObjectType()
class ProviderPaginationMeta {
  @Field()
  totalItems: number;

  @Field()
  itemCount: number;

  @Field()
  itemsPerPage: number;

  @Field()
  totalPages: number;

  @Field()
  currentPage: number;
}

@ObjectType()
export class PaginatedProviders {
  @Field(() => [Provider])
  providers: Provider[];

  @Field()
  meta: ProviderPaginationMeta;
}