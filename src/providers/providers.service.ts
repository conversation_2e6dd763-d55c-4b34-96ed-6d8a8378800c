import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Provider } from '../shared/entities/provider.entity';
import { CreateProviderInput } from './dto/create-provider.input';
import { UpdateProviderInput } from './dto/update-provider.input';
import { ProviderFilterInput } from './dto/provider-filter.input';
import { PaginatedProviders } from './responses/paginated-providers';
import { FileUpload } from 'graphql-upload';
import { FilesService } from '../files/files.service';

@Injectable()
export class ProvidersService {
  constructor(
    @InjectRepository(Provider)
    private providerRepository: Repository<Provider>,
    private readonly filesService: FilesService,
  ) {}

  async create(input: CreateProviderInput, logo?: FileUpload, userId?: number): Promise<Provider> {
    const provider = this.providerRepository.create(input);
    console.log(provider)

    // Set the createdById if a userId is provided
    if (userId) {
      provider.createdById = userId;
    }

    if (logo) {
      // Fix: Use saveFile instead of uploadFile
      const { createReadStream, filename, mimetype, encoding } = await logo;
      const logoUrl = await this.filesService.saveFile(createReadStream(), filename, mimetype, encoding);
      provider.logoUrl = filename;
    }

    return this.providerRepository.save(provider);
  }

  async findAll(): Promise<Provider[]> {
    return this.providerRepository.find({
      relations: ['createdBy']
    });
  }

  async findOne(id: number): Promise<Provider> {
    const provider = await this.providerRepository.findOne({
      where: { id },
      relations: ['createdBy']
    });
    if (!provider) {
      throw new NotFoundException(`Provider with ID ${id} not found`);
    }
    return provider;
  }

  async update(
    id: number,
    updateProviderDto: UpdateProviderInput,
    logo?: FileUpload,
    images?: FileUpload[]
  ): Promise<Provider> {
    // Ensure we're updating the correct provider
    const provider = await this.providerRepository.findOne({ where: { id } });
    if (!provider) {
      throw new NotFoundException(`Provider with ID ${id} not found`);
    }

    // Update provider fields with input data
    Object.assign(provider, updateProviderDto);

    // Handle logo upload if provided
    if (logo) {
      const { createReadStream, filename, mimetype, encoding } = await logo;
      const logoUrl = await this.filesService.saveFile(
        createReadStream(),
        filename,
        mimetype,
        encoding
      );
      provider.logoUrl = filename;
    }

    // Process new images if provided
    let newImageUrls: string[] = [];
    if (images && images.length > 0) {
      newImageUrls = await Promise.all(
        images.map(async (image) => {
          const { createReadStream, filename, mimetype, encoding } = await image;
          // Save the file using the provider-specific service
          await this.filesService.saveProvidersimage(
            createReadStream(),
            filename,
            mimetype,
            encoding,
            id
          );
          // Return the filename to store in the provider record
          return filename;
        })
      );
    }

    // Use existingImages if provided (even an empty array means "delete all existing images")
    if (updateProviderDto.hasOwnProperty('existingImages')) {
      // Set the base images from the DTO (could be empty)
      provider.imageUrls = updateProviderDto.existingImages || [];
      // Append any new images uploaded
      if (newImageUrls.length > 0) {
        provider.imageUrls = [...provider.imageUrls, ...newImageUrls];
      }
    } else {
      // If existingImages is not provided, merge new images with current images.
      // Optionally, remove any images specified for deletion.
      if (newImageUrls.length > 0) {
        provider.imageUrls = provider.imageUrls
          ? [...provider.imageUrls, ...newImageUrls]
          : newImageUrls;
      }
      if (updateProviderDto.removeImages && updateProviderDto.removeImages.length > 0) {
        provider.imageUrls = provider.imageUrls
          ? provider.imageUrls.filter(img => !updateProviderDto.removeImages?.includes(img))
          : [];
      }
    }

    // Save and return the updated provider
    return this.providerRepository.save(provider);
  }


  async remove(id: number): Promise<boolean> {
    const provider = await this.findOne(id);
    await this.providerRepository.remove(provider);
    return true;
  }

  async getFilteredProviders(filter: ProviderFilterInput = {}): Promise<PaginatedProviders> {
    const {
      search = '',
      type,
      ownership,
      country,
      city,
      category,
      state,
      page = 1,
      limit = 10
    } = filter;
    console.log(filter)

    const queryBuilder = this.providerRepository.createQueryBuilder('provider')
      .leftJoinAndSelect('provider.createdBy', 'createdBy');

    // Apply filters
    if (search && search.trim() !== '') {
      queryBuilder.andWhere(
        '(LOWER(provider.name) LIKE LOWER(:search) OR LOWER(provider.description) LIKE LOWER(:search))',
        { search: `%${search.trim()}%` }
      );
    }

    if (type) {
      queryBuilder.andWhere('provider.type = :type', { type });
    }

    if (ownership) {
      queryBuilder.andWhere('provider.ownership = :ownership', { ownership });
    }

    if (country) {
      queryBuilder.andWhere('provider.country = :country', { country });
    }

    if (city) {
      queryBuilder.andWhere('provider.city = :city', { city });
    }

    if (category) {
      queryBuilder.andWhere('provider.category = :category', { category });
    }
    if (state) {
      queryBuilder.andWhere('provider.state = :state', { state });
    }

    // Get total count before pagination
    const total = await queryBuilder.getCount();

    // Apply pagination
    const providers = await queryBuilder
      .orderBy('provider.name', 'ASC')
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();

    const meta = {
      totalItems: total,
      itemCount: providers.length,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page
    };

    return { providers, meta };
  }


  async getLogoProvider(filename: string): Promise<{ buffer: Buffer; mimetype: string } | null> {
    console.log(filename);

   let file=await this.filesService.getFile(filename)

    return file

  }

  async getProviderImage(providerId: number, filename: string): Promise<{ buffer: Buffer; mimetype: string } | null> {
    try {
      // Verify the provider exists
      await this.findOne(providerId);

      // Get the image file
      const file = await this.filesService.getProviderImage(providerId, filename);
      return file;
    } catch (error) {
      console.error(`Error retrieving provider image: ${error.message}`);
      return null;
    }
  }

  async getMyProviders(userId: number, filter: ProviderFilterInput = {}): Promise<PaginatedProviders> {
    const {
      search = '',
      type,
      ownership,
      country,
      city,
      category,
      state,
      page = 1,
      limit = 10
    } = filter;

    const queryBuilder = this.providerRepository.createQueryBuilder('provider')
      .leftJoinAndSelect('provider.createdBy', 'createdBy')
      .where('provider.createdById = :userId', { userId });

    // Apply filters
    if (search && search.trim() !== '') {
      queryBuilder.andWhere(
        '(LOWER(provider.name) LIKE LOWER(:search) OR LOWER(provider.description) LIKE LOWER(:search))',
        { search: `%${search.trim()}%` }
      );
    }

    if (type) {
      queryBuilder.andWhere('provider.type = :type', { type });
    }

    if (ownership) {
      queryBuilder.andWhere('provider.ownership = :ownership', { ownership });
    }

    if (country) {
      queryBuilder.andWhere('provider.country = :country', { country });
    }

    if (city) {
      queryBuilder.andWhere('provider.city = :city', { city });
    }

    if (category) {
      queryBuilder.andWhere('provider.category = :category', { category });
    }

    if (state) {
      queryBuilder.andWhere('provider.state = :state', { state });
    }

    // Get total count before pagination
    const total = await queryBuilder.getCount();

    // Apply pagination
    const providers = await queryBuilder
      .orderBy('provider.createdAt', 'DESC') // Order by creation date, newest first
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();

    const meta = {
      totalItems: total,
      itemCount: providers.length,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page
    };

    return { providers, meta };
  }

  async addImage(id: number, image: FileUpload): Promise<Provider> {
    const provider = await this.findOne(id);

    // Fix: Use saveFile instead of uploadFile
    const { createReadStream, filename, mimetype, encoding } = await image;
    const imageUrl = await this.filesService.saveFile(createReadStream(), filename, mimetype, encoding);

    if (!provider.imageUrls) {
      provider.imageUrls = [];
    }

    provider.imageUrls.push(imageUrl);

    return this.providerRepository.save(provider);
  }

  async removeImage(id: number, imageUrl: string): Promise<Provider> {
    const provider = await this.findOne(id);

    if (provider.imageUrls) {
      provider.imageUrls = provider.imageUrls.filter(url => url !== imageUrl);
    }

    return this.providerRepository.save(provider);
  }
}