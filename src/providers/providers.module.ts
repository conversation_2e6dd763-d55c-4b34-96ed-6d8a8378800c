import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Provider } from '../shared/entities/provider.entity';
import { ProvidersService } from './providers.service';
import { ProvidersResolver } from './providers.resolver';
import { FilesModule } from '../files/files.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Provider]),
    FilesModule
  ],
  providers: [ProvidersService, ProvidersResolver],
  exports: [ProvidersService]
})
export class ProvidersModule {}