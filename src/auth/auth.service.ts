// src/auth/auth.service.ts

import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../shared/entities/user.entity';
import * as argon2 from 'argon2'; // Import argon2
import { JwtService } from '@nestjs/jwt';
import { LoginInput } from './dto/login.input';
import { RegisterInput } from './dto/register.input';
import { RoleEnum } from '../shared/enums/user.roles.enum';
import LoginResponse from './response/login.response';
import { RegisterResponse } from './response/register.response';
import { ConfigService } from '@nestjs/config';
import { InvalidCredentialsError, UserExistsError } from '../shared/errors/auth.errors';
import { JwtPayload, AuthTokens } from './interfaces/auth.interface';
import { EmailService } from '../email/email.service';
import { ForgotPasswordInput } from './dto/forgot-password.input';
import { ResetPasswordInput } from './dto/reset-password.input';
import { ForgotPasswordResponse } from './response/forgot-password.response';
import { ResetPasswordResponse } from './response/reset-password.response';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    @InjectRepository(User) private readonly userRepository: Repository<User>,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly emailService: EmailService, // Add EmailService
  ) {}

  /**
   * Registers a new user and hashes their password using argon2.
   */
  async register(input: RegisterInput): Promise<RegisterResponse> {
    try {
      console.log(input);
      // Use a single query to check both email and username
      const existingUser = await this.userRepository.findOne({
        where: [
          { email: input.email },
          { username: input.username }
        ]
      })

      // Check which field caused the conflict and return specific error
      if (existingUser) {
        if (existingUser.email === input.email) {
          return {
            user: null,
            message: 'Registration failed',
            error: 'Email already in use',
            statusCode: 400
          };
        }
      }
      const hashedPassword = await this.hashPassword(input.password);

      const user = this.userRepository.create({
        email: input.email,
        username: input.username,
        password: hashedPassword,
        roles: [RoleEnum.USER]
      });

      let verificationToken = await this.jwtService.sign(
        { user },
        {
          secret: this.configService.get('VERIFICATION_EMAIL'),
          expiresIn: '1h' // Token will expire in 1 hour
        }
      );

      console.log(this.configService.get('VERIFICATION_EMAIL'))
      console.log(verificationToken)

      console.log(user.email)
      console.log(user.username)
     await this.emailService.sendVerificationEmail(
        user.email,
        input.username,
        verificationToken
      );


      return {
        user: user,
        message: 'Registration successful',
        statusCode: 201
      };
    } catch (error) {
      this.logger.error(`Registration failed: ${error.message}`);
      return {
        user: null,
        message: 'Registration failed',
        error: 'Internal server error',
        statusCode: 500
      };
    }
  }

  /**
   * Logs in a user and generates a JWT token.
   */
  async login(input: LoginInput): Promise<LoginResponse> {
    try {
      const user = await this.userRepository.findOne({
        where: { email: input.email },
        select: ['id', 'email',
          'password',
          'roles',"username",
          "profilePhoto",
          "coverPhoto"
        ],
      });

      if (!user) {
        return {
          user: null,
          message: 'Login failed',
          error: 'Invalid credentials',
          statusCode: 401
        };
      }

      const isPasswordValid = await this.verifyPassword(input.password, user.password);
      if (!isPasswordValid) {
        return {
          user: null,
          message: 'Login failed',
          error: 'Invalid credentials',
          statusCode: 401
        };
      }

      const tokens = await this.generateAuthTokens(user);
      const { password: _, ...userWithoutPassword } = user;

      return {
        user: userWithoutPassword as User,
        token: tokens.accessToken,
        message: 'Login successful',
        statusCode: 200
      };
    } catch (error) {
      this.logger.error(`Login failed: ${error.message}`);
      return {
        user: null,
        message: 'Login failed',
        error: 'Internal server error',
        statusCode: 500
      };
    }
  }

/**
 * Verifies a user's email using the verification token
 */
async verifyEmail(token: string): Promise<{ success: boolean; message: string; statusCode: number }> {
  try {
    console.log(token);
    // Verify and decode the token
    const decoded = await this.jwtService.verify(token, {
      secret: this.configService.get('VERIFICATION_EMAIL')
    });
    console.log(decoded);

    // The token structure has user object nested inside
    if (!decoded || !decoded.user || !decoded.user.email) {
      return {
        success: false,
        message: 'Invalid verification token structure',
        statusCode: 400
      };
    }

    // Find the user by email
    const user = await this.userRepository.findOne({
      where: { email: decoded.user.email }
    });

    if (!user) {
      // Create the user from the token data
      const newUser = this.userRepository.create({
        email: decoded.user.email,
        username: decoded.user.username,
        password: decoded.user.password,
        roles: decoded.user.roles,
      });

      await this.userRepository.save(newUser);

      return {
        success: true,
        message: 'Email verified and account created successfully',
        statusCode: 201
      };
    }

    // If user already exists
    return {
      success: true,
      message: 'User already exists',
      statusCode: 400
    };
  } catch (error) {
    this.logger.error(`Email verification failed: ${error.message}`);

    // Check if token is expired
    if (error.name === 'TokenExpiredError') {
      return {
        success: false,
        message: 'Verification token has expired',
        statusCode: 400
      };
    }

    return {
      success: false,
      message: `Email verification failed: ${error.message}`,
      statusCode: 500
    };
  }
}

  private async validateUser(email: string, password: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { email },
      select: ['id', 'email', 'password', 'roles'],
    });

    if (!user) {
      throw new InvalidCredentialsError(email);
    }

    const isPasswordValid = await this.verifyPassword(password, user.password);
    if (!isPasswordValid) {
      throw new InvalidCredentialsError(email);
    }

    const { password: _, ...result } = user;
    return result as User;
  }

  private async generateAuthTokens(user: User): Promise<AuthTokens> {
    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      roles: user.roles,
    };

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload),
      this.jwtService.signAsync(payload, {
        expiresIn: this.configService.get('auth.refreshTokenExpiresIn'),
      }),
    ]);

    return { accessToken, refreshToken };
  }

  private async hashPassword(password: string): Promise<string> {
    return argon2.hash(password);
  }

  private async verifyPassword(
    plainPassword: string,
    hashedPassword: string,
  ): Promise<boolean> {
    return argon2.verify(hashedPassword, plainPassword);
  }


  public async getProfile(id: number): Promise<User | null> {
    return await this.userRepository.findOne({
       where: { id } ,
        select: ['id', 'email',
           'username', 'roles', 'title', 'location', 'skills',
           'bio', 'coverPhoto', 'profilePhoto',
           'linkedin', 'github', 'website', 'x', 'mobile',
           'experiences', 'certifications', 'education'
          ]

    })
  }

  async forgotPassword(input: ForgotPasswordInput): Promise<ForgotPasswordResponse> {
    try {
      const user = await this.userRepository.findOne({
        where: { email: input.email },
        select: ['id', 'email', 'username'] // Added username to selection
      });

      if (!user) {
        // For security reasons, don't reveal if the email exists or not
        return {
          success: true,
          message: 'If your email is registered, you will receive a password reset link',
          statusCode: 200
        };
      }

      // Generate a password reset token
      const resetToken = await this.jwtService.sign(
        { sub: user.id, email: user.email },
        {
          secret: this.configService.get('auth.jwtSecret'),
          expiresIn: '1h' // Token expires in 1 hour
        }
      );

      // Send the password reset email
      await this.emailService.sendPasswordResetEmail(
        user.email,
        user.username ?? 'User',
        resetToken
      );

      return {
        success: true,
        message: 'Password reset link sent to your email',
        statusCode: 200
      };
    } catch (error) {
      this.logger.error(`Forgot password failed: ${error.message}`);
      return {
        success: false,
        message: 'Failed to process forgot password request',
        error: 'Internal server error',
        statusCode: 500
      };
    }
  }

  async resetPassword(input: ResetPasswordInput): Promise<ResetPasswordResponse> {
    try {
      // Verify the token
      const decoded = await this.jwtService.verify(input.token, {
        secret: this.configService.get('auth.jwtSecret')
      });

      if (!decoded || !decoded.sub) {
        return {
          success: false,
          message: 'Invalid or expired token',
          statusCode: 400
        };
      }

      // Find the user
      const user = await this.userRepository.findOne({
        where: { id: decoded.sub }
      });

      if (!user) {
        return {
          success: false,
          message: 'User not found',
          statusCode: 404
        };
      }

      // Hash the new password
      const hashedPassword = await this.hashPassword(input.newPassword);

      // Update the user's password
      user.password = hashedPassword;
      await this.userRepository.save(user);

      return {
        success: true,
        message: 'Password reset successful',
        statusCode: 200
      };
    } catch (error) {
      this.logger.error(`Reset password failed: ${error.message}`);

      if (error.name === 'TokenExpiredError') {
        return {
          success: false,
          message: 'Password reset token has expired',
          error: 'Token expired',
          statusCode: 400
        };
      }

      return {
        success: false,
        message: 'Failed to reset password',
        error: 'Internal server error',
        statusCode: 500
      };
    }
  }
}
