import { Field, ObjectType } from "@nestjs/graphql";
import { User } from "src/shared/entities/user.entity";

@ObjectType()
class LoginResponse {
    @Field(() => User, { nullable: true })
    user?: User | null;

    @Field(() => String, { nullable: true })
    token?: string;

    @Field(() => String, { nullable: true })
    message?: string;
  
    @Field({ nullable: true })
    error?: string;
  
    @Field({ nullable: true })
    statusCode?: number;


}

export default LoginResponse;