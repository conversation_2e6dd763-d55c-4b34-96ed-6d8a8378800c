// src/auth/auth.resolver.ts

import { Resolver, Mutation, Args, Context, Query } from '@nestjs/graphql';
import { AuthService } from './auth.service';
import { LoginInput } from './dto/login.input';
import { RegisterInput } from './dto/register.input';
import { User } from '../shared/entities/user.entity'; // Adjust the path as needed
import LoginResponse from './response/login.response';
import { RegisterResponse } from './response/register.response';
import { UseFilters, BadRequestException, UseGuards} from '@nestjs/common';
import { GraphQLErrorFilter } from '../shared/custom-exception.filter';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
// Add this import
import { VerifyEmailResponse } from './response/verify-email.response';
import { ForgotPasswordInput } from './dto/forgot-password.input';
import { ResetPasswordInput } from './dto/reset-password.input';
import { ForgotPasswordResponse } from './response/forgot-password.response';
import { ResetPasswordResponse } from './response/reset-password.response';


@Resolver(() => User)
export class AuthResolver {
  constructor(private readonly authService: AuthService) {}
  
  @UseFilters(GraphQLErrorFilter)
  @Mutation(() => LoginResponse)
  async login(@Args('input') input: LoginInput): Promise<LoginResponse> {
    const result = await this.authService.login(input);
    if (result.error) {
      throw new BadRequestException({
        error: result.error,
        statusCode: result.statusCode,
        message: result.message
      });
    }
    return result;
  }
  
  @UseFilters(GraphQLErrorFilter)
  @Mutation(() => RegisterResponse)
  async register(@Args('input') input: RegisterInput): Promise<RegisterResponse> {
    const result = await this.authService.register(input);
    if (result.error) {
      throw new BadRequestException({
        error: result.error,
        statusCode: result.statusCode,
        message: result.message
      });
    }
    return result;
  }

  @UseFilters(GraphQLErrorFilter)
  @Mutation(() => VerifyEmailResponse)
  async verifyEmail(@Args('token') token: string): Promise<VerifyEmailResponse> {
    const result = await this.authService.verifyEmail(token);
    
    if (!result.success && result.statusCode >= 400) {
      throw new BadRequestException({
        error: result.success,
        statusCode: result.statusCode,
        message: result.message
      });
    }
    
    return result;
  }

  @UseFilters(GraphQLErrorFilter)
  @Query(() => User)
  @UseGuards(JwtAuthGuard) 
  async getProfile(@Context() context): Promise<User | null> {

    if (!context.req.user) {
      throw new BadRequestException({
        error: 'Unauthorized',
        statusCode: 401,
        message: 'You must be logged in to view profile'
      });
    }
    let data=await this.authService.getProfile(context.req.user.id); 

    return data;
    

  }

  @Mutation(() => ForgotPasswordResponse)
  async forgotPassword(
    @Args('input') input: ForgotPasswordInput
  ): Promise<ForgotPasswordResponse> {
    return this.authService.forgotPassword(input);
  }

  @Mutation(() => ResetPasswordResponse)
  async resetPassword(
    @Args('input') input: ResetPasswordInput
  ): Promise<ResetPasswordResponse> {
    return this.authService.resetPassword(input);
  }
}
