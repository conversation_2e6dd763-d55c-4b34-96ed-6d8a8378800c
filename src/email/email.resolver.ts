import { Resolver, Mutation, Args, Query } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { EmailService } from './email.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorator';
import { RoleEnum } from '../shared/enums/user.roles.enum';

@Resolver()
export class EmailResolver {
  constructor(private readonly emailService: EmailService) {}

  @Mutation(() => Boolean)
  async testSendEmail(
    @Args('email') email: string,
    @Args('subject', { nullable: true }) subject?: string,
    @Args('text', { nullable: true }) text?: string,
  ): Promise<boolean> {
    return this.emailService.sendEmail({
      to: email,
      subject: subject || 'Test Email',
      text: text || 'This is a test email from the backend system.',
    });
  }

  @Mutation(() => Boolean)

  async sendWelcomeEmailTest(
    @Args('email') email: string,
    @Args('username') username: string,
  ): Promise<boolean> {
    return this.emailService.sendWelcomeEmail(email, username);
  }
}