import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import * as handlebars from 'handlebars';
import * as fs from 'fs';
import * as path from 'path';

export interface EmailOptions {
  to: string | string[];
  subject: string;
  template?: string;
  context?: Record<string, any>;
  html?: string;
  text?: string;
  attachments?: Array<{
    filename: string;
    content?: any;
    path?: string;
    contentType?: string;
  }>;
}

@Injectable()
export class EmailService {
  private transporter: nodemailer.Transporter;
  private readonly logger = new Logger(EmailService.name);

  constructor(private configService: ConfigService) {
    this.initializeTransporter();
  }

  private initializeTransporter() {
    const host = this.configService.get<string>('EMAIL_HOST');
    const port = this.configService.get<number>('EMAIL_PORT');
    const user = this.configService.get<string>('EMAIL_USER');
    const pass = this.configService.get<string>('EMAIL_PASSWORD');
    const secure = this.configService.get<boolean>('EMAIL_SECURE', false);

    if (!host || !port || !user || !pass) {
      this.logger.warn('Email configuration is incomplete. Email sending will be disabled.');
      return;
    }

    this.transporter = nodemailer.createTransport({
      host,
     /* port,
      secure,*/
      auth: {
        user,
        pass,
      },
      // Add these options for TLS
      tls: {
        rejectUnauthorized: false,
        ciphers: 'SSLv3'
      }
    });

    // Verify connection
    this.transporter.verify((error) => {
      if (error) {
        this.logger.error('Email service connection error:', error);
      } else {
        this.logger.log('Email service is ready to send messages');
      }
    });
  }

  async sendEmail(options: EmailOptions): Promise<boolean> {
    if (!this.transporter) {
      this.logger.warn('Email transporter not initialized. Email not sent.');
      return false;
    }

    try {
      const { to, subject, template, context, html, text, attachments } = options;

      let htmlContent = html;
      let textContent = text;

      // If template is provided, compile it with handlebars
      if (template && context) {
        const templatePath = path.join(process.cwd(), 'src/email/templates', `${template}.hbs`);

        if (fs.existsSync(templatePath)) {
          const templateSource = fs.readFileSync(templatePath, 'utf8');
          const compiledTemplate = handlebars.compile(templateSource);
          htmlContent = compiledTemplate(context);
        } else {
          this.logger.warn(`Template ${template} not found at ${templatePath}`);
        }
      }

      const mailOptions = {
        from: this.configService.get<string>('EMAIL_FROM', '<EMAIL>'),
        to,
        subject,
        html: htmlContent,
        text: textContent,
        attachments,
      };

      const info = await this.transporter.sendMail(mailOptions);
      this.logger.log(`Email sent: ${info.messageId}`);
      return true;
    } catch (error) {
      this.logger.error('Failed to send email:', error);
      return false;
    }
  }

  async sendWelcomeEmail(email: string, username: string): Promise<boolean> {
    return this.sendEmail({
      to: email,
      subject: 'Welcome to Our Platform',
      template: 'welcome',
      context: {
        username,
        loginUrl: this.configService.get<string>('FRONTEND_URL', 'http://localhost:3000') + '/login',
      },
    });
  }

  async sendPasswordResetEmail(
    email: string,
    username: string,
    token: string
  ): Promise<boolean> {
    const resetUrl = `${this.configService.get('CLIENT_URL', 'http://localhost:8080')}/reset-password?token=${token}`;

    return this.sendEmail({
      to: email,
      subject: 'Reset Your Password - Knowledge Hubster',
      template: 'reset-password',
      context: {
        username,
        email,
        resetUrl,
        year: new Date().getFullYear(),
      },
    });
  }

  async sendProviderCreationConfirmation(email: string, providerName: string): Promise<boolean> {
    return this.sendEmail({
      to: email,
      subject: `Provider Registration: ${providerName}`,
      template: 'provider-confirmation',
      context: {
        providerName,
        dashboardUrl: this.configService.get<string>('FRONTEND_URL', 'http://localhost:3000') + '/providers/dashboard',
      },
    });
  }

  async sendVerificationEmail(email: string, username: string, verificationToken: string): Promise<boolean> {
    const verificationUrl = `${this.configService.get<string>('FRONTEND_URL', 'http://localhost:8080')}/verify-email?token=${verificationToken}`;

    return this.sendEmail({
      to: email,
      subject: 'Verify Your Email - Knowledge Hubster',
      template: 'register',
      context: {
        username,
        email,
        verificationUrl,
        year: new Date().getFullYear(),
      },
    });
  }

  async sendProviderWelcomeEmail(email: string, username: string, password: string): Promise<boolean> {
    const loginUrl = `${this.configService.get<string>('FRONTEND_URL', 'http://localhost:8080')}/login`;

    return this.sendEmail({
      to: email,
      subject: 'Welcome to Knowledge Hubster as a Provider',
      template: 'provider-welcome',
      context: {
        username,
        email,
        password,
        loginUrl,
        year: new Date().getFullYear(),
      },
    });
  }
}