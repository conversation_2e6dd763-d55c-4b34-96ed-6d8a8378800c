import { Module } from '@nestjs/common';
import { GraphQLModule } from '@nestjs/graphql';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { AppResolver } from './app.resolver';
import { join } from 'path';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { FilesModule } from './files/files.module';
import { TrainingModule } from './training/training.module';
import { ApolloServerPluginLandingPageLocalDefault } from 'apollo-server-core';
import { GraphQLUpload } from 'graphql-upload';

// Add this to your imports
import { EnrollmentsModule } from './enrollments/enrollments.module';
import { ProvidersModule } from './providers/providers.module';
import { EmailModule } from './email/email.module';
import { AdminModule } from './admin/admin.module';

// Add EnrollmentsModule to the imports array in the @Module decorator
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env']
    }),
    GraphQLModule.forRoot<ApolloDriverConfig>({
      driver: ApolloDriver,
      autoSchemaFile: join(process.cwd(), 'src/schema.gql'),
      sortSchema: true,
      playground: true,
      context: ({ req }) => ({ req }),
      buildSchemaOptions: {
        dateScalarMode: 'timestamp',
      },
      resolvers: {
        Upload: GraphQLUpload,
      },
      csrfPrevention: false,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        url: 'postgresql://neondb_owner:<EMAIL>/neondb',
        ssl: {
          rejectUnauthorized: true,
        },
        autoLoadEntities: true,
        synchronize: true, // Changed to true temporarily
        logging: true, // Enable logging to see SQL queries
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
      }),
      inject: [ConfigService]
    }),
    UsersModule,
    AuthModule,
    FilesModule,
    TrainingModule,
    EnrollmentsModule,
    ProvidersModule,
    EmailModule,
    AdminModule

  ],
  providers: [AppResolver]
})
export class AppModule {}
