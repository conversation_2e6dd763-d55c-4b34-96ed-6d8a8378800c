import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { graphqlUploadExpress } from 'graphql-upload';
import { BadRequestException, Logger, ValidationPipe } from '@nestjs/common';
import { GraphQLErrorFilter } from './shared/custom-exception.filter';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // GraphQL Upload middleware must come before CORS
  app.use(graphqlUploadExpress({ 
    maxFileSize: 10_000_000, // 10 MB
    maxFiles: 10 
  }));

  // Updated CORS configuration to include localhost:8080
  app.enableCors({
    origin: [
      'https://knowledgehubster.vercel.app',
      'https://knowledgehubster-git-main-med661s-projects.vercel.app',
      'https://knowledgehubster-*.vercel.app',
      'http://localhost:8080', // Added local frontend URL
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: [
      'Content-Type', 
      'Authorization', 
      'Apollo-Require-Preflight',
      'x-apollo-operation-name',
      'x-apollo-operation-id',
      'apollo-query-plan-experimental',
      'apollo-require-preflight',
      'x-apollo-tracing',
      'Accept',
      'Origin',
      'X-Requested-With'
    ],
    exposedHeaders: ['Set-Cookie'],
    maxAge: 86400, // 24 hours in seconds
  });

  // ✅ Global Validation Pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      exceptionFactory: (errors) => {
        const formattedErrors = errors.reduce((accumulator, error) => {
          accumulator[error.property] = Object.values(error.constraints || {}).join(', ');
          return accumulator;
        }, {});
        console.log('formattedErrors:', formattedErrors);
        throw new BadRequestException(formattedErrors);
      },
    }),
  );

  // ✅ Global Exception Filter
  app.useGlobalFilters(new GraphQLErrorFilter());

  // ✅ Start Server
  await app.listen(process.env.PORT || 3000);
  Logger.log(`🚀 Server running on http://localhost:${process.env.PORT || 3000}/graphql`);
}

bootstrap();