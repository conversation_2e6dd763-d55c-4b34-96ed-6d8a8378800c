import { InputType, Field, Int } from '@nestjs/graphql';
import { GraphQLUpload } from 'graphql-upload';

@InputType()
class InstructorInput {
  @Field()
  name: string;
  @Field()
  email: string;
  @Field()
  specialization: string;
}

@InputType()
class ModuleInput {
  @Field()
  title: string;
  @Field()
  description: string;
}

@InputType()
class LabInput {
  @Field()
  title: string;
  @Field()
  description: string;
}

@InputType()
class ResourceInput {
  @Field()
  name: string;
  @Field()
  description: string;
}

@InputType()
export class CreateTrainingInput {
  @Field()
  title: string;
  
  // Add providerId field
  @Field(() => Int)
  providerId: number;
  
  // Keep provider field for backward compatibility
  @Field({ nullable: true })
  provider?: string;
  
  @Field()
  price: number;
  @Field()
  duration: string;
  @Field()
  level: string;
  @Field()
  format: string;
  @Field()
  category: string;
  @Field()
  startDate: Date;
  @Field()
  description: string;
  @Field(() => String, { nullable: true }) // السماح بتمرير `image` كـ null
  image?: string;
  @Field()
  instructor: InstructorInput;
  @Field(() => [String])
  requirements: string[];
  @Field()
  language: string;
  @Field()
  certificate: boolean;
  @Field(() => [ModuleInput])
  modules: ModuleInput[];
  @Field(() => [LabInput])
  labs: LabInput[];
  @Field(() => [ResourceInput])
  resources: ResourceInput[];
  @Field({ nullable: true })
  location?: string;
}