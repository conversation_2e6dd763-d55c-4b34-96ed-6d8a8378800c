import { Field, InputType } from "@nestjs/graphql";
import { Int, Float } from "type-graphql";

@InputType()
export class TrainingFilterInput {
  @Field(() => String, { nullable: true })
  search?: string;
  
  @Field(() => String, { nullable: true })
  category?: string;
  
  @Field(() => String, { nullable: true })
  level?: string;
  
  @Field(() => String, { nullable: true })
  format?: string;
  
  @Field(() => Boolean, { nullable: true })
  certificate?: boolean;
  
  @Field(() => String, { nullable: true })
  location?: string;
  
  @Field(() => Float, { nullable: true })
  minPrice?: number;
  
  @Field(() => Float, { nullable: true })
  maxPrice?: number;
  
  @Field(() => Int, { nullable: true, defaultValue: 1 })
  page?: number;
  
  @Field(() => Int, { nullable: true, defaultValue: 10 })
  limit?: number;
}