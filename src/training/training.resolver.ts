import { Resolver, Mutation, Args, Query, Context } from '@nestjs/graphql';
import { Training } from "../shared/entities/training.entity"
import { TrainingService } from './training.service';
import { CreateTrainingInput } from './dto/create-training.input';
import { FileUpload, GraphQLUpload } from 'graphql-upload';
import { BadRequestException } from '@nestjs/common';
import { PaginatedTrainings } from './responses/paginated-training';
import { TrainingFilterInput } from './dto/training-filter.input';

import { Int, Float } from 'type-graphql';
import { FileResponse } from '../shared/dtos/file-response.type';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorator';
import { RoleEnum } from '../shared/enums/user.roles.enum';
import { UpdateTrainingInput } from './dto/update-training.input';

@Resolver(() => Training)
export class TrainingResolver {
  constructor(private readonly trainingService: TrainingService) {}

  @Mutation(() => Training)
  @UseGuards(JwtAuthGuard)
  @Roles(RoleEnum.USER)
  async createTraining(
    @Args('input') createTrainingInput: CreateTrainingInput,
    @Args({ name: 'image', type: () => GraphQLUpload, nullable: true }) image?: FileUpload,
    @Context() context?: any
  ): Promise<Training> {
    console.log("ggggg")
    console.log('CreateTrainingInput:', JSON.stringify(createTrainingInput, null, 2));

    // Extract user ID from JWT token
    const userId = context?.req?.user?.id;
    if (!userId) {
      throw new BadRequestException('User ID not found in token');
    }

    // Extract input from context if the original input is empty
    let finalInput = createTrainingInput;
    if (!finalInput || Object.keys(finalInput).length === 0) {
      try {
        // Check if input might be in the context variables
        const variables = context?.req?.body?.variables;
        if (variables && variables.input) {
          console.log('Found input in context variables:', variables.input);
          finalInput = variables.input;
        }
      } catch (error) {
        console.error('Error extracting input from context:', error);
      }
    }

    // Validate required fields
    if (!finalInput || !finalInput.title) {
      throw new BadRequestException('Training title is required');
    }

    console.log('Final input to use:', JSON.stringify(finalInput, null, 2));

    // Log image details if available
    if (image) {
      console.log('Image details:', {
        filename: image?.filename,
        mimetype: image?.mimetype,
        encoding: image?.encoding,
        hasCreateReadStream: !!image?.createReadStream
      });
    } else {
      console.log('No image provided');
    }

    let imageUrl: string | undefined = undefined;

    // Only process the image if it exists and is a valid FileUpload
    if (image && typeof image === 'object' && 'createReadStream' in image) {
      try {
        imageUrl = await this.trainingService.uploadImage(image);
        console.log('Image uploaded successfully:', imageUrl);
      } catch (error) {
        console.error('Error uploading image:', error);
      }
    } else {
      console.log('No image provided or invalid image format');
    }

    // Log the data being sent to create method
    console.log('Creating training with:', {
      inputData: JSON.stringify(finalInput, null, 2),
      imageUrl,
      userId
    });

    try {
      const result = await this.trainingService.create(finalInput, imageUrl, userId);

      // Verify the result has required fields before returning
      if (!result || !result.title) {
        throw new Error('Training created but missing required fields');
      }

      return result;
    } catch (error) {
      console.log("ggggggg")
      console.error('Error creating training:', error);
      throw new Error(`Failed to create training: ${error.message}`);
    }
  }

  @Mutation(() => Training)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RoleEnum.USER)
  async updateTraining(
    @Args('input') updateTrainingInput: UpdateTrainingInput,
    @Args({ name: 'image', type: () => GraphQLUpload, nullable: true }) image?: FileUpload
  ): Promise<Training> {
    console.log('UpdateTrainingInput:', JSON.stringify(updateTrainingInput, null, 2));

    // Check if user is trying to update provider information
    if (updateTrainingInput.providerId !== undefined || updateTrainingInput.provider !== undefined) {
      console.warn('Warning: Provider information cannot be updated. The original provider will be preserved.');
    }
    console.log(image)
    let imageUrl: string | undefined = undefined;

    // Only process the image if it exists and is a valid FileUpload
    if (image && typeof image === 'object' && 'createReadStream' in image) {
      try {
        imageUrl = await this.trainingService.uploadImage(image);
        console.log('Image uploaded successfully:', imageUrl);
      } catch (error) {
        console.error('Error uploading image:', error);
      }
    }

    try {
      const result = await this.trainingService.update(
        updateTrainingInput.id,
        updateTrainingInput,
        imageUrl
      );

      return result;
    } catch (error) {
      console.error('Error updating training:', error);
      throw new Error(`Failed to update training: ${error.message}`);
    }
  }

  @Query(() => String)
  helloWorld(): string {
    return "Hello, GraphQL!";
  }

  @Query(() => [Training])
  async trainings(): Promise<Training[]> {
    return this.trainingService.findAll();
  }

  @Query(() => PaginatedTrainings)
  async filteredTrainings(
    @Args('search', { type: () => String, nullable: true }) search: string,
    @Args('category', { type: () => String, nullable: true }) category: string,
    @Args('level', { type: () => String, nullable: true }) level: string,
    @Args('format', { type: () => String, nullable: true }) format: string,
    @Args('certificate', { type: () => Boolean, nullable: true }) certificate: boolean,
    @Args('location', { type: () => String, nullable: true }) location: string,
    @Args('minPrice', { type: () => Float, nullable: true }) minPrice: number,
    @Args('maxPrice', { type: () => Float, nullable: true }) maxPrice: number,
    @Args('page', { type: () => Int, nullable: true, defaultValue: 1 }) page: number,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 10 }) limit: number
  ): Promise<PaginatedTrainings> {
    const filter = {
      search,
      category,
      level,
      format,
      certificate,
      location,
      minPrice,
      maxPrice,
      page,
      limit
    };
    console.log("Received filter in resolver:", filter);

    const result = await this.trainingService.findFiltered(filter);

    return {
      trainings: result.trainings,
      meta: result.meta
    };
  }

  @Query(() => FileResponse, { nullable: true })
  async getTrainingImageByName(
    @Args('filename') filename: string
  ): Promise<FileResponse | null> {
    const file = await this.trainingService.getTrainingImageByName(filename);
    if (!file) {
      return null;
    }

    return {
      base64Data: file.buffer.toString('base64'),
      mimetype: file.mimetype
    };
  }

  @Query(() => Training, { nullable: true })
  async getTrainingById(
    @Args('id', { type: () => Int }) id: number
  ): Promise<Training | null> {
    return this.trainingService.findById(id);
  }

  @Query(() => PaginatedTrainings, { name: 'myTrainingsWithFilter' })
  @UseGuards(JwtAuthGuard)
  async getMyTrainingsWithFilter(
    @Context() context: { req: { user: { id: number } } },
    @Args('filter', { nullable: true }) filter?: TrainingFilterInput
  ): Promise<PaginatedTrainings> {
    const userId = context.req.user.id;
    const result = await this.trainingService.getMyTrainings(userId, filter || {});

    return {
      trainings: result.trainings,
      meta: result.meta
    };
  }

  @Query(() => PaginatedTrainings, { name: 'myTrainings' })
  @UseGuards(JwtAuthGuard)
  async getMyTrainings(
    @Context() context: { req: { user: { id: number } } },
    @Args('search', { type: () => String, nullable: true }) search?: string,
    @Args('category', { type: () => String, nullable: true }) category?: string,
    @Args('level', { type: () => String, nullable: true }) level?: string,
    @Args('format', { type: () => String, nullable: true }) format?: string,
    @Args('certificate', { type: () => Boolean, nullable: true }) certificate?: boolean,
    @Args('location', { type: () => String, nullable: true }) location?: string,
    @Args('minPrice', { type: () => Float, nullable: true }) minPrice?: number,
    @Args('maxPrice', { type: () => Float, nullable: true }) maxPrice?: number,
    @Args('page', { type: () => Int, nullable: true, defaultValue: 1 }) page?: number,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 10 }) limit?: number
  ): Promise<PaginatedTrainings> {
    const userId = context.req.user.id;

    // Create filter object from individual parameters
    const filter = {
      search,
      category,
      level,
      format,
      certificate,
      location,
      minPrice,
      maxPrice,
      page,
      limit
    };

    const result = await this.trainingService.getMyTrainings(userId, filter);

    return {
      trainings: result.trainings,
      meta: result.meta
    };
  }

  @Mutation(() => Boolean)
  // Only admins can delete trainings
  async deleteTraining(
    @Args('id', { type: () => Int }) id: number
  ): Promise<boolean> {
    return this.trainingService.deleteTraining(id);
  }
}

