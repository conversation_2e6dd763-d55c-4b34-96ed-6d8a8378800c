import { ObjectType, Field } from '@nestjs/graphql';
import { Int } from 'type-graphql';
import { Training } from '../../shared/entities/training.entity';

@ObjectType()
export class TrainingPaginationMeta {
  @Field(() => Int)
  totalItems: number;
  
  @Field(() => Int)
  itemCount: number;
  
  @Field(() => Int)
  itemsPerPage: number;
  
  @Field(() => Int)
  totalPages: number;
  
  @Field(() => Int)
  currentPage: number;
}

@ObjectType()
export class PaginatedTrainings {
  @Field(() => [Training])
  trainings: Training[];
  
  @Field(() => TrainingPaginationMeta)
  meta: TrainingPaginationMeta;
}