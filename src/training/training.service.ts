import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Training } from "../shared/entities/training.entity"
import { CreateTrainingInput } from './dto/create-training.input';
import { createWriteStream } from 'fs';
import { join } from 'path';
import * as fs from 'fs';
import { mkdir } from 'fs/promises';
import { TrainingFilterInput } from './dto/training-filter.input';
import { TrainingPaginationMeta } from './responses/paginated-training';
import { FilesService } from '../files/files.service';
import { NotFoundException } from '@nestjs/common';
import { Provider } from '../shared/entities/provider.entity';
import { UpdateTrainingInput } from './dto/update-training.input';

@Injectable()
export class TrainingService {
  constructor(
    @InjectRepository(Training)
    private trainingRepository: Repository<Training>,
    @InjectRepository(Provider)
    private providerRepository: Repository<Provider>,
    private readonly filesService: FilesService,
  ) {}

  async create(createTrainingInput: CreateTrainingInput, imageUrl?: string, userId?: number): Promise<Training> {
    // Validate input
    if (!createTrainingInput || !createTrainingInput.title) {
      throw new Error('Training title is required');
    }

    // Verify that the provider exists
    const provider = await this.providerRepository.findOne({
      where: { id: createTrainingInput.providerId }
    });

    if (!provider) {
      throw new NotFoundException(`Provider with ID ${createTrainingInput.providerId} not found`);
    }

    console.log('Creating training with input:', JSON.stringify(createTrainingInput, null, 2));

    // Ensure requirements is properly formatted as an array
    if (createTrainingInput.requirements && !Array.isArray(createTrainingInput.requirements)) {
      console.log('Converting requirements to proper array format');
      createTrainingInput.requirements = Array.isArray(createTrainingInput.requirements)
        ? createTrainingInput.requirements
        : [];
    }

    // Create a new training entity
    const training = this.trainingRepository.create({
      ...createTrainingInput,
      // Set both provider and providerId for backward compatibility
      provider: provider.name,
      providerId: provider.id,
      image: imageUrl || 'default-image.jpg', // Provide a default image if none is uploaded
      createdBy: userId, // Add the createdBy field
    });

    // Get count of existing trainings
    const trainingCount = await this.trainingRepository.count();
    console.log(`Current number of trainings: ${trainingCount}`);

    // Save and return the entity
    const savedTraining = await this.trainingRepository.save(training);
    console.log('Saved training:', JSON.stringify(savedTraining, null, 2));
    return savedTraining;
  }

  async uploadImage(image: any): Promise<string> {
    const { createReadStream, filename } = await image;
    const uploadDir = join(process.cwd(), 'uploads');
    const uniqueFilename = `${Date.now()}-${filename}`;
    const filePath = join(uploadDir, uniqueFilename);

    // Create uploads directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    return new Promise((resolve, reject) =>
      createReadStream()
        .pipe(createWriteStream(filePath))
        .on('finish', () => resolve(uniqueFilename))
        .on('error', (error) => reject(error))
    );
  }

  async findAll(): Promise<Training[]> {
    return this.trainingRepository.find();
  }

  async findFiltered(filter: TrainingFilterInput): Promise<{ trainings: Training[], meta: TrainingPaginationMeta }> {
    const {
      search = '',
      category = '',
      level = '',
      format = '',
      certificate,
      location = '',
      minPrice,
      maxPrice,
      page = 1,
      limit = 10
    } = filter || {};

    const queryBuilder = this.trainingRepository.createQueryBuilder('training');

    if (search && search.trim() !== '') {
      queryBuilder.andWhere(
        '(training.title ILIKE :search OR training.description ILIKE :search OR training.provider ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    if (category && category.trim() !== '') {
      queryBuilder.andWhere('training.category ILIKE :category', { category: `%${category}%` });
    }

    if (level && level.trim() !== '') {
      queryBuilder.andWhere('training.level ILIKE :level', { level: `%${level}%` });
    }

    if (format && format.trim() !== '') {
      queryBuilder.andWhere('training.format ILIKE :format', { format: `%${format}%` });
    }

    if (certificate !== undefined && certificate !== null) {
      queryBuilder.andWhere('training.certificate = :certificate', { certificate });
    }

    if (location && location.trim() !== '') {
      queryBuilder.andWhere('training.location ILIKE :location', { location: `%${location}%` });
    }

    // Add price range filtering
    if (minPrice !== undefined && minPrice !== null) {
      queryBuilder.andWhere('training.price >= :minPrice', { minPrice });
    }

    if (maxPrice !== undefined && maxPrice !== null) {
      queryBuilder.andWhere('training.price <= :maxPrice', { maxPrice });
    }

    const total = await queryBuilder.getCount();

    queryBuilder
      .orderBy('training.id', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    const trainings = await queryBuilder.getMany();

    const meta = {
      totalItems: total,
      itemCount: trainings.length,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page
    };

    return { trainings, meta };
  }

  async getTrainingImageByName(filename: string): Promise<{ buffer: Buffer; mimetype: string } | null> {
      console.log(filename);

     let file=await this.filesService.getFile(filename)

      return file
  }

  async getMyTrainings(userId: number, filter: TrainingFilterInput): Promise<{ trainings: Training[], meta: TrainingPaginationMeta }> {
    const {
      search = '',
      category = '',
      level = '',
      format = '',
      certificate,
      location = '',
      minPrice,
      maxPrice,
      page = 1,
      limit = 10
    } = filter || {};

    const queryBuilder = this.trainingRepository.createQueryBuilder('training')
      .where('training.createdBy = :userId', { userId });

    if (search && search.trim() !== '') {
      queryBuilder.andWhere(
        '(training.title ILIKE :search OR training.description ILIKE :search OR training.provider ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    if (category && category.trim() !== '') {
      queryBuilder.andWhere('training.category ILIKE :category', { category: `%${category}%` });
    }

    if (level && level.trim() !== '') {
      queryBuilder.andWhere('training.level ILIKE :level', { level: `%${level}%` });
    }

    if (format && format.trim() !== '') {
      queryBuilder.andWhere('training.format ILIKE :format', { format: `%${format}%` });
    }

    if (certificate !== undefined && certificate !== null) {
      queryBuilder.andWhere('training.certificate = :certificate', { certificate });
    }

    if (location && location.trim() !== '') {
      queryBuilder.andWhere('training.location ILIKE :location', { location: `%${location}%` });
    }

    if (minPrice !== undefined && minPrice !== null) {
      queryBuilder.andWhere('training.price >= :minPrice', { minPrice });
    }

    if (maxPrice !== undefined && maxPrice !== null) {
      queryBuilder.andWhere('training.price <= :maxPrice', { maxPrice });
    }

    const total = await queryBuilder.getCount();

    queryBuilder
      .orderBy('training.id', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    const trainings = await queryBuilder.getMany();

    const meta = {
      totalItems: total,
      itemCount: trainings.length,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page
    };

    return { trainings, meta };
  }

  async findById(id: number): Promise<Training | null> {
    try {
      // Use a query builder for more control over the relation loading
      const training = await this.trainingRepository
        .createQueryBuilder('training')
        .leftJoinAndSelect('training.providerEntity', 'provider')
        .where('training.id = :id', { id })
        .getOne();

      if (!training) {
        console.log(`No training found with id: ${id}`);
        return null;
      }

      // If providerEntity is still null, try to load it manually
      if (!training.providerEntity && training.providerId) {
        const provider = await this.providerRepository.findOne({
          where: { id: Number(training.providerId) }
        });

        if (provider) {
          training.providerEntity = provider;
        }
      }

      console.log('Raw requirements:', training);

      // Handle PostgreSQL array format conversion
      if (training.requirements) {
        // If it's a string that looks like a PostgreSQL array literal
        if (typeof training.requirements === 'string') {
          const reqString = training.requirements as string;
          if (reqString.startsWith('{') && reqString.endsWith('}')) {
            console.log('Converting PostgreSQL array format to JavaScript array');
            // Convert PostgreSQL array format to JavaScript array
            const arrayString = reqString.substring(1, reqString.length - 1);
            training.requirements = arrayString.split(',')
              .map(item => item.replace(/^"(.*)"$/, '$1').trim());
          }
        } else if (!Array.isArray(training.requirements)) {
          console.log(`Fixing requirements field for training ${id}`);
          training.requirements = [];
        }
      } else {
        training.requirements = [];
      }

      console.log('Processed requirements:', training.requirements);

      return training;
    } catch (error) {
      console.error(`Error fetching training with id ${id}:`, error);
      throw new NotFoundException(`Training with ID ${id} not found`);
    }
  }

  async deleteTraining(id: number): Promise<boolean> {
    // Check if training exists
    const training = await this.trainingRepository.findOne({
      where: { id }
    });

    if (!training) {
      throw new NotFoundException(`Training with ID ${id} not found`);
    }

    try {
      // First, delete all enrollments associated with this training
      // Use a query builder to delete enrollments by trainingId
      await this.trainingRepository.manager.createQueryBuilder()
        .delete()
        .from('enrollment')
        .where("trainingId = :id", { id })
        .execute();

      // Then delete the training
      const result = await this.trainingRepository.delete(id);

      // Return true if deletion was successful
      return result && result.affected ? result.affected > 0 : false;
    } catch (error) {
      console.error(`Error deleting training with ID ${id}:`, error);
      throw new Error(`Failed to delete training: ${error.message}`);
    }
  }

  async update(id: number, updateTrainingInput: UpdateTrainingInput, imageUrl?: string): Promise<Training> {
    // Find the training by ID
    const training = await this.trainingRepository.findOne({ where: { id } });

    if (!training) {
      throw new NotFoundException(`Training with ID ${id} not found`);
    }

    // Remove provider-related fields from the update input to preserve the original provider
    if (updateTrainingInput.providerId !== undefined) {
      console.log('Removing providerId from update to preserve original provider');
      delete updateTrainingInput.providerId;
    }

    if (updateTrainingInput.provider !== undefined) {
      console.log('Removing provider name from update to preserve original provider');
      delete updateTrainingInput.provider;
    }

    // Handle requirements array if provided
    if (updateTrainingInput.requirements && !Array.isArray(updateTrainingInput.requirements)) {
      updateTrainingInput.requirements = Array.isArray(updateTrainingInput.requirements)
        ? updateTrainingInput.requirements
        : [];
    }

    // Update the image if a new one is provided
    if (imageUrl) {
      updateTrainingInput.image = imageUrl;
    }

    // Merge the existing training with the updates
    const updatedTraining = this.trainingRepository.merge(training, updateTrainingInput);

    // Save and return the updated entity
    return this.trainingRepository.save(updatedTraining);
  }
}