import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Training } from '../shared/entities/training.entity';
import { Provider } from '../shared/entities/provider.entity';
import { TrainingService } from './training.service';
import { TrainingResolver } from './training.resolver';
import { FilesModule } from '../files/files.module';
import { ProvidersModule } from '../providers/providers.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Training, Provider]),
    FilesModule,
    ProvidersModule
  ],
  providers: [TrainingService, TrainingResolver],
})
export class TrainingModule {}