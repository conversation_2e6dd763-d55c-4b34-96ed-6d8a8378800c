import { Field, InputType } from "@nestjs/graphql";
import { Int, Float } from "type-graphql";

@InputType()
export class EnrollmentFilterInput {
  @Field(() => Int, { nullable: true })
  trainingId?: number;
  
  @Field(() => String, { nullable: true })
  status?: string;
  

  @Field(() => String, { nullable: true })
  search?: string;
  
  @Field(() => Int, { nullable: true, defaultValue: 1 })
  page?: number;
  
  @Field(() => Int, { nullable: true, defaultValue: 10 })
  limit?: number;
}