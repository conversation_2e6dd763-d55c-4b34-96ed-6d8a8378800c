import { InputType, Field, Int } from '@nestjs/graphql';
import { IsEnum, IsInt, IsNotEmpty } from 'class-validator';

@InputType()
export class UpdateEnrollmentInput {
  @Field(() => Int)
  @IsInt()
  @IsNotEmpty()
  id: number;

  @Field()
  @IsEnum(['PENDING', 'APPROVED', 'REJECTED', 'COMPLETED', 'CANCELLED'], {
    message: 'Status must be one of: PENDING, APPROVED, REJECTED, COMPLETED, CANCELLED'
  })
  @IsNotEmpty()
  status: string;
}
