import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Enrollment } from '../shared/entities/enrollment.entity';
import { Training } from '../shared/entities/training.entity';
import { User } from '../shared/entities/user.entity';
import { EnrollmentsService } from './enrollments.service';
import { EnrollmentsResolver } from './enrollments.resolver';

@Module({
  imports: [
    TypeOrmModule.forFeature([Enrollment, Training, User]),
  ],
  providers: [EnrollmentsService, EnrollmentsResolver],
  exports: [EnrollmentsService],
})
export class EnrollmentsModule {}