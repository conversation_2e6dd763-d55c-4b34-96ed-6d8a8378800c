import { Injectable, NotFoundException, ConflictException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Enrollment } from '../shared/entities/enrollment.entity';
import { Training } from '../shared/entities/training.entity';
import { User } from '../shared/entities/user.entity';
import { EnrollmentFilterInput } from './dto/enrollment-filter.input';
import { PaginatedEnrollments } from './responses/paginated-enrollments';
import { UpdateEnrollmentInput } from './dto/update-enrollment.input';

@Injectable()
export class EnrollmentsService {
  constructor(
    @InjectRepository(Enrollment)
    private enrollmentRepository: Repository<Enrollment>,
    @InjectRepository(Training)
    private trainingRepository: Repository<Training>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async enrollUserInTraining(userId: number, trainingId: number): Promise<Enrollment> {
    console.log("gggg")
    // Check if user exists
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Check if training exists
    const training = await this.trainingRepository.findOne({ where: { id: trainingId } });
    if (!training) {
      throw new NotFoundException(`Training with ID ${trainingId} not found`);
    }

    // Check if user is already enrolled
    const existingEnrollment = await this.enrollmentRepository.findOne({
      where: { userId, trainingId }
    });

    if (existingEnrollment) {
      throw new ConflictException('User is already enrolled in this training');
    }
    console.log('ggggg'+existingEnrollment)


    // Create new enrollment
    const enrollment = this.enrollmentRepository.create({
      userId,
      trainingId,
    });

    console.log('ggggg'+enrollment)
    // Save enrollment
    const savedEnrollment = await this.enrollmentRepository.save(enrollment);

    // Update training enrollments count and add user ID to enrolled users
    training.enrollments = (training.enrollments || 0) + 1;

    // Initialize enrolledUserIds array if it doesn't exist
    if (!training.enrolledUserIds) {
      training.enrolledUserIds = [];
    }

    // Add user ID to the array if not already present
    if (!training.enrolledUserIds.includes(userId)) {
      training.enrolledUserIds.push(userId);
    }

    await this.trainingRepository.save(training);

    return savedEnrollment;
  }

  async getUserEnrollments(userId: number): Promise<Enrollment[]> {
    return this.enrollmentRepository.find({
      where: { userId },
      relations: ['training'],
    });
  }

  async getTrainingEnrollments(trainingId: number): Promise<Enrollment[]> {
    return this.enrollmentRepository.find({
      where: { trainingId },
      relations: ['user'],
    });
  }



  async cancelEnrollment(userId: number, trainingId: number): Promise<boolean> {
    const enrollment = await this.enrollmentRepository.findOne({
      where: { userId, trainingId }
    });

    if (!enrollment) {
      throw new NotFoundException('Enrollment not found');
    }

    // Decrement training enrollments count and remove user ID
    const training = await this.trainingRepository.findOne({ where: { id: trainingId } });
    if (training) {
      training.enrollments = Math.max(0, (training.enrollments || 0) - 1);

      // Remove user ID from enrolledUserIds array
      if (training.enrolledUserIds && Array.isArray(training.enrolledUserIds)) {
        training.enrolledUserIds = training.enrolledUserIds.filter(id => id !== userId);
      }

      await this.trainingRepository.save(training);
    }

    await this.enrollmentRepository.remove(enrollment);
    return true;
  }

  // Add this method to your EnrollmentsService class

  // Updated method to filter enrollments
  async getFilteredEnrollments(filter: EnrollmentFilterInput = {}): Promise<PaginatedEnrollments> {
    // Ensure filter is an object
    const safeFilter = filter || {};

    // Parse and validate filter parameters
    const {
      trainingId,
      status,

      search = '',
      page = 1,
      limit = 10
    } = safeFilter;

    console.log('Service received filter parameters:', {
      trainingId: `${trainingId} (${typeof trainingId})`,
      status: `${status} (${typeof status})`,
      search: `${search} (${typeof search})`,
      page: `${page} (${typeof page})`,
      limit: `${limit} (${typeof limit})`
    });

    // Create a raw query to debug what's happening
    console.log('Attempting direct query with trainingId:', trainingId);
    const directQuery = await this.enrollmentRepository.find({
      where: { trainingId: trainingId }
    });
    console.log('Direct query results:', directQuery.length, 'enrollments');
    console.log('Direct query training IDs:', directQuery.map(e => e.trainingId));

    // Create query builder with explicit where clause
    const queryBuilder = this.enrollmentRepository
      .createQueryBuilder('enrollment')
      .leftJoinAndSelect('enrollment.user', 'user');

    // Fix for trainingId - ensure it's treated as a number
    if (trainingId !== undefined && trainingId !== null) {
      // Convert to number explicitly
      const parsedTrainingId = Number(trainingId);
      console.log(`Filtering by trainingId: ${parsedTrainingId}`);

      // Use parameter binding for safety
      queryBuilder.andWhere('enrollment."trainingId" = :trainingId', {
        trainingId: parsedTrainingId
      });
    }

    // Fix for status - ensure case matching
    if (status) {
      console.log(`Filtering by status: ${status}`);
      queryBuilder.andWhere('enrollment.status = :status', {
        status: status
      });
    }

    // Fix for progress range



    // Handle search with proper string sanitization
    if (search && typeof search === 'string' && search.trim() !== '') {
      console.log(`Filtering by search: ${search.trim()}`);
      queryBuilder.andWhere(
        '(LOWER(user.username) LIKE LOWER(:search) OR LOWER(user.email) LIKE LOWER(:search))',
        { search: `%${search.trim()}%` }
      );
    }

    // Log the final SQL query and parameters for debugging
    const sqlQuery = queryBuilder.getSql();
    const parameters = queryBuilder.getParameters();
    console.log('Generated SQL:', sqlQuery);
    console.log('Query parameters:', parameters);

    // Get total count before pagination
    const total = await queryBuilder.getCount();

    // Apply pagination
    const enrollments = await queryBuilder
      .orderBy('enrollment.enrolledAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();

    // Log the results for verification
    console.log(`Found ${enrollments.length} enrollments out of ${total} total`);
    console.log('Enrollment IDs:', enrollments.map(e => e.id));
    console.log('Training IDs:', enrollments.map(e => e.trainingId));

    const meta = {
      totalItems: total,
      itemCount: enrollments.length,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page
    };

    return { enrollments, meta };
  }

  /**
   * Updates an enrollment's status
   * @param input The enrollment update input containing id and status
   * @param userId The ID of the user making the update (for permission checking)
   * @returns The updated enrollment
   */
  async updateEnrollment(input: UpdateEnrollmentInput, userId: number): Promise<Enrollment> {
    // Find the enrollment by ID
    const enrollment = await this.enrollmentRepository.findOne({
      where: { id: input.id },
      relations: ['training']
    });

    if (!enrollment) {
      throw new NotFoundException(`Enrollment with ID ${input.id} not found`);
    }

    // Check if the user is authorized to update this enrollment
    // Allow if the user is the creator of the training or an admin
    const training = await this.trainingRepository.findOne({
      where: { id: enrollment.trainingId }
    });

    if (!training) {
      throw new NotFoundException(`Training with ID ${enrollment.trainingId} not found`);
    }

    // Only allow the training creator to update enrollment status
    if (training.createdBy !== userId) {
      throw new ForbiddenException('You are not authorized to update this enrollment');
    }

    // Update the enrollment status
    enrollment.status = input.status;

    // Save and return the updated enrollment
    return this.enrollmentRepository.save(enrollment);
  }
}