import { ObjectType, Field } from '@nestjs/graphql';
import { Int } from 'type-graphql';
import { Enrollment } from '../../shared/entities/enrollment.entity';

@ObjectType()
export class EnrollmentPaginationMeta {
  @Field(() => Int)
  totalItems: number;
  
  @Field(() => Int)
  itemCount: number;
  
  @Field(() => Int)
  itemsPerPage: number;
  
  @Field(() => Int)
  totalPages: number;
  
  @Field(() => Int)
  currentPage: number;
}

@ObjectType()
export class PaginatedEnrollments {
  @Field(() => [Enrollment])
  enrollments: Enrollment[];
  
  @Field(() => EnrollmentPaginationMeta)
  meta: EnrollmentPaginationMeta;
}