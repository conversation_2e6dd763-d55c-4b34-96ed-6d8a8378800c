import { Resolver, Mutation, Query, Args, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { EnrollmentsService } from './enrollments.service';
import { Enrollment } from '../shared/entities/enrollment.entity';
import { Int, Float } from 'type-graphql';
import { RolesGuard } from '../auth/guards/roles.guard';
import { RoleEnum } from '../shared/enums/user.roles.enum';
import { Roles } from 'src/auth/decorator';
import { EnrollmentFilterInput } from './dto/enrollment-filter.input';
import { PaginatedEnrollments } from './responses/paginated-enrollments';
import { UpdateEnrollmentInput } from './dto/update-enrollment.input';

@Resolver(() => Enrollment)
export class EnrollmentsResolver {
  constructor(private readonly enrollmentsService: EnrollmentsService) {}

  @Mutation(() => Enrollment)
  @UseGuards(JwtAuthGuard)
  async enrollInTraining(
    @Args('trainingId', { type: () => Int }) trainingId: number,
    @Context() context: any
  ): Promise<Enrollment> {
    console.log("gggg")
    const userId = context.req.user.id;
    return this.enrollmentsService.enrollUserInTraining(userId, trainingId);
  }

  // Updated query with individual arguments instead of filter object
  @Query(() => PaginatedEnrollments, { name: 'trainingEnrollments' })
  async getFilteredEnrollments(
    @Args('trainingId', { type: () => Int, nullable: true }) trainingId?: number,
    @Args('status', { type: () => String, nullable: true }) status?: string,
    @Args('search', { type: () => String, nullable: true }) search?: string,
    @Args('page', { type: () => Int, nullable: true, defaultValue: 1 }) page?: number,
    @Args('limit', { type: () => Int, nullable: true, defaultValue: 10 }) limit?: number
  ): Promise<PaginatedEnrollments> {
    console.log('Received individual parameters:', {
      trainingId,
      status,
      search,
      page,
      limit
    });

    // Create filter object from individual parameters
    const filter: EnrollmentFilterInput = {
      trainingId,
      status,
      search,
      page,
      limit
    };

    return this.enrollmentsService.getFilteredEnrollments(filter);
  }

  // Keep the original method for backward compatibility
  @Query(() => [Enrollment])
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RoleEnum.USER)
  async getTrainingEnrollments(
    @Args('trainingId', { type: () => Int }) trainingId: number
  ): Promise<Enrollment[]> {
    return this.enrollmentsService.getTrainingEnrollments(trainingId);
  }

  @Query(() => [Enrollment])
  @UseGuards(JwtAuthGuard)
  async myEnrollments(@Context() context: any): Promise<Enrollment[]> {
    const userId = context.req.user.id;
    return this.enrollmentsService.getUserEnrollments(userId);
  }


  @Mutation(() => Boolean)
  @UseGuards(JwtAuthGuard)
  async cancelEnrollment(
    @Args('trainingId', { type: () => Int }) trainingId: number,
    @Context() context: any
  ): Promise<boolean> {
    const userId = context.req.user.id;
    return this.enrollmentsService.cancelEnrollment(userId, trainingId);
  }

  @Mutation(() => Enrollment)
  @UseGuards(JwtAuthGuard)
  async updateEnrollment(
    @Args('input') input: UpdateEnrollmentInput,
    @Context() context: any
  ): Promise<Enrollment> {
    const userId = context.req.user.id;
    return this.enrollmentsService.updateEnrollment(input, userId);
  }
}