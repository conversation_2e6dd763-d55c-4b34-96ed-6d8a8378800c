import { Field, InputType } from '@nestjs/graphql';
import { IsEmail, IsString, Min<PERSON>ength, MaxLength, IsOptional } from 'class-validator';

@InputType()
export class CreateProviderUserInput {
  @Field()
  @IsString()
  @MinLength(3)
  @MaxLength(50)
  username: string;

  @Field()
  @IsEmail()
  email: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  title?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  location?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  mobile?: string;
}
