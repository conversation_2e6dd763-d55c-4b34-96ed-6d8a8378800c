import { Field, ObjectType } from '@nestjs/graphql';
import { User } from '../../shared/entities/user.entity';

@ObjectType()
export class CreateProviderUserResponse {
  @Field(() => User, { nullable: true })
  user: User | null;

  @Field()
  message: string;

  @Field({ nullable: true })
  error?: string;

  @Field()
  statusCode: number;

  @Field({ nullable: true })
  generatedPassword?: string;
}
