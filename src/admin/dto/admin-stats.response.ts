import { ObjectType, Field, Int, Float } from '@nestjs/graphql';

@ObjectType()
export class UserRoleDistribution {
  @Field()
  role: string;

  @Field(() => Int)
  count: number;
}

@ObjectType()
export class UserStats {
  @Field(() => Int)
  total: number;

  @Field(() => Int)
  newUsersToday: number;

  @Field(() => Int)
  newUsersThisWeek: number;

  @Field(() => Int)
  newUsersThisMonth: number;

  @Field(() => [UserRoleDistribution])
  roleDistribution: UserRoleDistribution[];
}

@ObjectType()
export class ProviderStats {
  @Field(() => Int)
  total: number;

  @Field(() => Int)
  newProvidersToday: number;

  @Field(() => Int)
  newProvidersThisWeek: number;

  @Field(() => Int)
  newProvidersThisMonth: number;
}

@ObjectType()
export class TrainingStats {
  @Field(() => Int)
  total: number;

  @Field(() => Int)
  newTrainingsToday: number;

  @Field(() => Int)
  newTrainingsThisWeek: number;

  @Field(() => Int)
  newTrainingsThisMonth: number;

  @Field(() => Float)
  averagePrice: number;
}

@ObjectType()
export class EnrollmentStats {
  @Field(() => Int)
  total: number;

  @Field(() => Int)
  newEnrollmentsToday: number;

  @Field(() => Int)
  newEnrollmentsThisWeek: number;

  @Field(() => Int)
  newEnrollmentsThisMonth: number;

  @Field(() => Float)
  totalRevenue: number;
}

@ObjectType()
export class CategoryDistribution {
  @Field()
  category: string;

  @Field(() => Int)
  count: number;
}

@ObjectType()
export class AdminStats {
  @Field(() => UserStats)
  users: UserStats;

  @Field(() => ProviderStats)
  providers: ProviderStats;

  @Field(() => TrainingStats)
  trainings: TrainingStats;

  @Field(() => EnrollmentStats)
  enrollments: EnrollmentStats;

  @Field(() => [CategoryDistribution])
  trainingCategories: CategoryDistribution[];
}
