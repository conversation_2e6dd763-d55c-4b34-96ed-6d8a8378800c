import { Resolver, Query, Args, Mutation } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorator';
import { RoleEnum } from '../shared/enums/user.roles.enum';
import { AdminService } from './admin.service';
import { AdminStats } from './dto/admin-stats.response';
import { TimePeriodInput } from './dto/time-period.input';
import { CreateProviderUserInput } from './dto/create-provider-user.input';
import { CreateProviderUserResponse } from './dto/create-provider-user.response';

@Resolver()
export class AdminResolver {
  constructor(private readonly adminService: AdminService) {}

  @Query(() => AdminStats)
  /*@UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RoleEnum.ADMIN)*/
  async adminStats(
    @Args('timePeriod', { nullable: true }) timePeriod?: TimePeriodInput
  ): Promise<AdminStats> {
    return this.adminService.getAdminStats(timePeriod);
  }

  @Mutation(() => CreateProviderUserResponse)
  /*@UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(RoleEnum.ADMIN)*/
  async createProviderUser(
    @Args('input') input: CreateProviderUserInput
  ): Promise<CreateProviderUserResponse> {
    return this.adminService.createProviderUser(input);
  }
}
