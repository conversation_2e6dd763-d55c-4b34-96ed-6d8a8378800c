query GetAdminStats($timePeriod: TimePeriodInput) {
  # You can pass a time period to filter the statistics
  # Example: { "timePeriod": { "startDate": "2023-01-01", "endDate": "2023-12-31" } }
  adminStats(timePeriod: $timePeriod) {
    users {
      total
      newUsersToday
      newUsersThisWeek
      newUsersThisMonth
      roleDistribution {
        role
        count
      }
    }
    providers {
      total
      newProvidersToday
      newProvidersThisWeek
      newProvidersThisMonth
    }
    trainings {
      total
      newTrainingsToday
      newTrainingsThisWeek
      newTrainingsThisMonth
      averagePrice
    }
    enrollments {
      total
      newEnrollmentsToday
      newEnrollmentsThisWeek
      newEnrollmentsThisMonth
      totalRevenue
    }
    trainingCategories {
      category
      count
    }
  }
}
