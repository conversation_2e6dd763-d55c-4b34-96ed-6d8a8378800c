import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../shared/entities/user.entity';
import { Provider } from '../shared/entities/provider.entity';
import { Training } from '../shared/entities/training.entity';
import { Enrollment } from '../shared/entities/enrollment.entity';
import { AdminService } from './admin.service';
import { AdminResolver } from './admin.resolver';
import { EmailModule } from '../email/email.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Provider, Training, Enrollment]),
    EmailModule,
  ],
  providers: [AdminService, AdminResolver],
  exports: [AdminService],
})
export class AdminModule {}
