import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../shared/entities/user.entity';
import { Provider } from '../shared/entities/provider.entity';
import { Training } from '../shared/entities/training.entity';
import { Enrollment } from '../shared/entities/enrollment.entity';
import { TimePeriodInput } from './dto/time-period.input';
import {
  AdminStats,
  UserStats,
  ProviderStats,
  TrainingStats,
  EnrollmentStats,
  CategoryDistribution,
  UserRoleDistribution
} from './dto/admin-stats.response';
import { CreateProviderUserInput } from './dto/create-provider-user.input';
import { CreateProviderUserResponse } from './dto/create-provider-user.response';
import { RoleEnum } from '../shared/enums/user.roles.enum';
import * as argon2 from 'argon2';
import { EmailService } from '../email/email.service';
import { generateRandomPassword } from '../shared/utils/password.utils';

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name);

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,

    @InjectRepository(Provider)
    private providerRepository: Repository<Provider>,

    @InjectRepository(Training)
    private trainingRepository: Repository<Training>,

    @InjectRepository(Enrollment)
    private enrollmentRepository: Repository<Enrollment>,

    private readonly emailService: EmailService,
  ) {}

  async getAdminStats(timePeriod?: TimePeriodInput): Promise<AdminStats> {
    const [
      userStats,
      providerStats,
      trainingStats,
      enrollmentStats,
      trainingCategories
    ] = await Promise.all([
      this.getUserStats(timePeriod),
      this.getProviderStats(timePeriod),
      this.getTrainingStats(timePeriod),
      this.getEnrollmentStats(timePeriod),
      this.getTrainingCategoryDistribution()
    ]);

    return {
      users: userStats,
      providers: providerStats,
      trainings: trainingStats,
      enrollments: enrollmentStats,
      trainingCategories
    };
  }

  private async getUserStats(timePeriod?: TimePeriodInput): Promise<UserStats> {
    // Set default time periods
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const oneWeekAgo = new Date(today);
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const oneMonthAgo = new Date(today);
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    // Apply custom time period if provided
    const startDate = timePeriod?.startDate;
    const endDate = timePeriod?.endDate;

    // Build query based on time period
    let query = this.userRepository.createQueryBuilder('user');

    if (startDate && endDate) {
      query = query.where('user."createdAt" BETWEEN :startDate AND :endDate', { startDate, endDate });
    }

    // Get all users
    const allUsers = await this.userRepository.find();
    const total = allUsers.length;

    // Since User entity doesn't have createdAt field, we'll use default values
    const newUsersToday = 0;
    const newUsersThisWeek = 0;
    const newUsersThisMonth = 0;

    // Get role distribution - using a simpler approach to avoid SQL syntax issues

    // Then, count the occurrences of each role
    const roleCounts = {};
    allUsers.forEach(user => {
      if (user.roles && Array.isArray(user.roles)) {
        user.roles.forEach(role => {
          roleCounts[role] = (roleCounts[role] || 0) + 1;
        });
      }
    });

    // Convert to the expected format
    const roleDistribution: UserRoleDistribution[] = Object.entries(roleCounts)
      .map(([role, count]) => ({
        role,
        count: count as number
      }))
      .sort((a, b) => b.count - a.count);

    return {
      total,
      newUsersToday,
      newUsersThisWeek,
      newUsersThisMonth,
      roleDistribution
    };
  }

  private async getProviderStats(timePeriod?: TimePeriodInput): Promise<ProviderStats> {
    // Set default time periods
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const oneWeekAgo = new Date(today);
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const oneMonthAgo = new Date(today);
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    // Apply custom time period if provided
    const startDate = timePeriod?.startDate;
    const endDate = timePeriod?.endDate;

    // Build query based on time period
    let query = this.providerRepository.createQueryBuilder('provider');

    if (startDate && endDate) {
      query = query.where('provider."createdAt" BETWEEN :startDate AND :endDate', { startDate, endDate });
    }

    // Get all providers
    const allProviders = await this.providerRepository.find();
    const total = allProviders.length;

    // Count new providers based on createdAt date
    const newProvidersToday = allProviders.filter(provider => provider.createdAt && provider.createdAt >= today).length;
    const newProvidersThisWeek = allProviders.filter(provider => provider.createdAt && provider.createdAt >= oneWeekAgo).length;
    const newProvidersThisMonth = allProviders.filter(provider => provider.createdAt && provider.createdAt >= oneMonthAgo).length;

    return {
      total,
      newProvidersToday,
      newProvidersThisWeek,
      newProvidersThisMonth
    };
  }

  private async getTrainingStats(timePeriod?: TimePeriodInput): Promise<TrainingStats> {
    // Set default time periods
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const oneWeekAgo = new Date(today);
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const oneMonthAgo = new Date(today);
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    // Apply custom time period if provided
    const startDate = timePeriod?.startDate;
    const endDate = timePeriod?.endDate;

    // Build query based on time period
    let query = this.trainingRepository.createQueryBuilder('training');

    if (startDate && endDate) {
      query = query.where('training."createdAt" BETWEEN :startDate AND :endDate', { startDate, endDate });
    }

    // Get all trainings
    const allTrainings = await this.trainingRepository.find();
    const total = allTrainings.length;

    // Calculate average price
    const totalPrice = allTrainings.reduce((sum, training) => sum + (training.price || 0), 0);
    const averagePrice = total > 0 ? totalPrice / total : 0;

    // Count new trainings based on createdAt date
    const newTrainingsToday = allTrainings.filter(training => training.createdAt && training.createdAt >= today).length;
    const newTrainingsThisWeek = allTrainings.filter(training => training.createdAt && training.createdAt >= oneWeekAgo).length;
    const newTrainingsThisMonth = allTrainings.filter(training => training.createdAt && training.createdAt >= oneMonthAgo).length;

    return {
      total,
      newTrainingsToday,
      newTrainingsThisWeek,
      newTrainingsThisMonth,
      averagePrice
    };
  }

  private async getEnrollmentStats(timePeriod?: TimePeriodInput): Promise<EnrollmentStats> {
    // Set default time periods
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const oneWeekAgo = new Date(today);
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const oneMonthAgo = new Date(today);
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    // Apply custom time period if provided
    const startDate = timePeriod?.startDate;
    const endDate = timePeriod?.endDate;

    // Build query based on time period
    let query = this.enrollmentRepository.createQueryBuilder('enrollment');

    if (startDate && endDate) {
      query = query.where('enrollment."enrolledAt" BETWEEN :startDate AND :endDate', { startDate, endDate });
    }

    // Get all enrollments
    const allEnrollments = await this.enrollmentRepository.find({ relations: ['training'] });
    const total = allEnrollments.length;

    // Count new enrollments based on enrolledAt date
    const newEnrollmentsToday = allEnrollments.filter(enrollment => enrollment.enrolledAt && enrollment.enrolledAt >= today).length;
    const newEnrollmentsThisWeek = allEnrollments.filter(enrollment => enrollment.enrolledAt && enrollment.enrolledAt >= oneWeekAgo).length;
    const newEnrollmentsThisMonth = allEnrollments.filter(enrollment => enrollment.enrolledAt && enrollment.enrolledAt >= oneMonthAgo).length;

    // Calculate total revenue
    const totalRevenue = allEnrollments.reduce((sum, enrollment) => {
      return sum + (enrollment.training?.price || 0);
    }, 0);

    return {
      total,
      newEnrollmentsToday,
      newEnrollmentsThisWeek,
      newEnrollmentsThisMonth,
      totalRevenue
    };
  }

  private async getTrainingCategoryDistribution(): Promise<CategoryDistribution[]> {
    // Using a simpler approach to avoid SQL syntax issues
    // First, get all trainings with their categories
    const trainings = await this.trainingRepository.find();

    // Then, count the occurrences of each category
    const categoryCounts = {};
    trainings.forEach(training => {
      if (training.category) {
        const category = training.category;
        categoryCounts[category] = (categoryCounts[category] || 0) + 1;
      }
    });

    // Convert to the expected format
    return Object.entries(categoryCounts)
      .map(([category, count]) => ({
        category,
        count: count as number
      }))
      .sort((a, b) => b.count - a.count);
  }

  /**
   * Creates a new user with the PROVIDER role and sends them an email with login credentials
   * @param input The provider user creation input
   * @returns Response with user data and status information
   */
  async createProviderUser(input: CreateProviderUserInput): Promise<CreateProviderUserResponse> {
    try {
      // Check if user with this email already exists
      const existingUser = await this.userRepository.findOne({
        where: { email: input.email }
      });

      if (existingUser) {
        return {
          user: null,
          message: 'User creation failed',
          error: 'Email already in use',
          statusCode: 400
        };
      }

      // Generate a random password
      const password = generateRandomPassword();
      const hashedPassword = await argon2.hash(password);

      // Create the user with PROVIDER role
      const user = this.userRepository.create({
        email: input.email,
        username: input.username || `provider_${Date.now()}`, // Ensure username is never undefined
        password: hashedPassword,
        roles: [RoleEnum.PROVIDER, RoleEnum.USER],
        title: input.title,
        location: input.location,
        mobile: input.mobile
      });

      // Save the user
      const savedUser = await this.userRepository.save(user);

      // Send welcome email with credentials
      await this.emailService.sendProviderWelcomeEmail(
        user.email,
        user.username || input.username, // Use input.username as fallback
        password // Send the plain text password in the email
      );

      this.logger.log(`Created provider user: ${savedUser.email}`);

      return {
        user: savedUser,
        message: 'Provider user created successfully',
        statusCode: 201,
        generatedPassword: password // Include the password in the response for admin reference
      };
    } catch (error) {
      this.logger.error(`Failed to create provider user: ${error.message}`, error.stack);
      return {
        user: null,
        message: 'User creation failed',
        error: error.message,
        statusCode: 500
      };
    }
  }
}
