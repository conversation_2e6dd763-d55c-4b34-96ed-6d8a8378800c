{"name": "backend-system", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node --max-old-space-size=8192 --gc-interval=100 -r ts-node/register dist/main.js", "start:dev": "NODE_OPTIONS=\"--max-old-space-size=8192\" nest start --watch", "start:debug": "NODE_OPTIONS=\"--max-old-space-size=8192 --gc-interval=100\" nest start --debug --watch", "start:prod": "node --max-old-space-size=8192 --gc-interval=100 dist/main", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@apollo/server": "^4.11.3", "@nestjs/apollo": "^13.0.3", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.1", "@nestjs/graphql": "^13.0.3", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.10", "@nestjs/typeorm": "^11.0.0", "@types/argon2": "^0.14.1", "@types/handlebars": "^4.0.40", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "apollo-server-express": "^3.13.0", "argon2": "^0.41.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "graphql": "^16.10.0", "graphql-tools": "^9.0.14", "graphql-upload": "^13.0.0", "handlebars": "^4.7.8", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.13.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "type-graphql": "^2.0.0-rc.2", "typeorm": "^0.3.20"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/graphql-upload": "^8.0.12", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^15.14.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}